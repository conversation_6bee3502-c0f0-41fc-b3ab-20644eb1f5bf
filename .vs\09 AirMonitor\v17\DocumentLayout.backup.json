{"Version": 1, "WorkspaceRootPath": "D:\\09 AirMonitor\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|d:\\09 airmonitor\\resources\\themes\\styles\\calendar.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|solutionrelative:resources\\themes\\styles\\calendar.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|d:\\09 airmonitor\\resources\\themes\\styles\\buttongroup.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|solutionrelative:resources\\themes\\styles\\buttongroup.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|d:\\09 airmonitor\\resources\\themes\\styles\\border.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|solutionrelative:resources\\themes\\styles\\border.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|d:\\09 airmonitor\\resources\\themes\\styles\\button.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|solutionrelative:resources\\themes\\styles\\button.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|d:\\09 airmonitor\\resources\\themes\\styles\\badge.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|solutionrelative:resources\\themes\\styles\\badge.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|d:\\09 airmonitor\\resources\\themes\\styles\\menu.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|solutionrelative:resources\\themes\\styles\\menu.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|d:\\09 airmonitor\\resources\\themes\\styles\\contextmenu.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|solutionrelative:resources\\themes\\styles\\contextmenu.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|d:\\09 airmonitor\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|solutionrelative:app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 8, "Children": [{"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:135:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d84ee353-0bef-5a41-a649-8f89aca5d84d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Calendar.xaml", "DocumentMoniker": "D:\\09 AirMonitor\\Resources\\Themes\\Styles\\Calendar.xaml", "RelativeDocumentMoniker": "Resources\\Themes\\Styles\\Calendar.xaml", "ToolTip": "D:\\09 AirMonitor\\Resources\\Themes\\Styles\\Calendar.xaml*", "RelativeToolTip": "Resources\\Themes\\Styles\\Calendar.xaml*", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T06:24:53.956Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "ButtonGroup.xaml", "DocumentMoniker": "D:\\09 AirMonitor\\Resources\\Themes\\Styles\\ButtonGroup.xaml", "RelativeDocumentMoniker": "Resources\\Themes\\Styles\\ButtonGroup.xaml", "ToolTip": "D:\\09 AirMonitor\\Resources\\Themes\\Styles\\ButtonGroup.xaml", "RelativeToolTip": "Resources\\Themes\\Styles\\ButtonGroup.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T06:24:46.392Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "Border.xaml", "DocumentMoniker": "D:\\09 AirMonitor\\Resources\\Themes\\Styles\\Border.xaml", "RelativeDocumentMoniker": "Resources\\Themes\\Styles\\Border.xaml", "ToolTip": "D:\\09 AirMonitor\\Resources\\Themes\\Styles\\Border.xaml", "RelativeToolTip": "Resources\\Themes\\Styles\\Border.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T06:24:28.101Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "Button.xaml", "DocumentMoniker": "D:\\09 AirMonitor\\Resources\\Themes\\Styles\\Button.xaml", "RelativeDocumentMoniker": "Resources\\Themes\\Styles\\Button.xaml", "ToolTip": "D:\\09 AirMonitor\\Resources\\Themes\\Styles\\Button.xaml", "RelativeToolTip": "Resources\\Themes\\Styles\\Button.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T06:24:19.673Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "Badge.xaml", "DocumentMoniker": "D:\\09 AirMonitor\\Resources\\Themes\\Styles\\Badge.xaml", "RelativeDocumentMoniker": "Resources\\Themes\\Styles\\Badge.xaml", "ToolTip": "D:\\09 AirMonitor\\Resources\\Themes\\Styles\\Badge.xaml", "RelativeToolTip": "Resources\\Themes\\Styles\\Badge.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T06:23:54.755Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "Menu.xaml", "DocumentMoniker": "D:\\09 AirMonitor\\Resources\\Themes\\Styles\\Menu.xaml", "RelativeDocumentMoniker": "Resources\\Themes\\Styles\\Menu.xaml", "ToolTip": "D:\\09 AirMonitor\\Resources\\Themes\\Styles\\Menu.xaml", "RelativeToolTip": "Resources\\Themes\\Styles\\Menu.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T06:23:37.706Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "ContextMenu.xaml", "DocumentMoniker": "D:\\09 AirMonitor\\Resources\\Themes\\Styles\\ContextMenu.xaml", "RelativeDocumentMoniker": "Resources\\Themes\\Styles\\ContextMenu.xaml", "ToolTip": "D:\\09 AirMonitor\\Resources\\Themes\\Styles\\ContextMenu.xaml", "RelativeToolTip": "Resources\\Themes\\Styles\\ContextMenu.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T06:23:18.477Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "App.xaml", "DocumentMoniker": "D:\\09 AirMonitor\\App.xaml", "RelativeDocumentMoniker": "App.xaml", "ToolTip": "D:\\09 AirMonitor\\App.xaml", "RelativeToolTip": "App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T06:18:48.587Z", "EditorCaption": ""}]}]}]}