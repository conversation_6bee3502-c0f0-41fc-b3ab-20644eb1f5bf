using CommunityToolkit.Mvvm.ComponentModel;
using Microsoft.Extensions.Logging;

namespace AirMonitor.ViewModels
{
    /// <summary>
    /// ViewModel 基类，提供通用功能
    /// </summary>
    public abstract class ViewModelBase : ObservableObject
    {
        protected readonly ILogger Logger;

        protected ViewModelBase(ILogger logger)
        {
            Logger = logger;
        }

        /// <summary>
        /// 视图模型标题
        /// </summary>
        public virtual string Title { get; protected set; } = string.Empty;

        /// <summary>
        /// 是否正在加载
        /// </summary>
        private bool _isLoading;
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// 错误消息
        /// </summary>
        private string _errorMessage = string.Empty;
        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        /// <summary>
        /// 是否有错误
        /// </summary>
        public bool HasError => !string.IsNullOrEmpty(ErrorMessage);

        /// <summary>
        /// 清除错误消息
        /// </summary>
        protected void ClearError()
        {
            ErrorMessage = string.Empty;
        }

        /// <summary>
        /// 设置错误消息
        /// </summary>
        /// <param name="message">错误消息</param>
        protected void SetError(string message)
        {
            ErrorMessage = message;
            Logger.LogError("ViewModel错误: {Message}", message);
        }

        /// <summary>
        /// 初始化方法，在视图加载时调用
        /// </summary>
        public virtual void Initialize()
        {
            Logger.LogDebug("{ViewModelName} 初始化", GetType().Name);
        }

        /// <summary>
        /// 清理方法，在视图卸载时调用
        /// </summary>
        public virtual void Cleanup()
        {
            Logger.LogDebug("{ViewModelName} 清理", GetType().Name);
        }
    }
}
