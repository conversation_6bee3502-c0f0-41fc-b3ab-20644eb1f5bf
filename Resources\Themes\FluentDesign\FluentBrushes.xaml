<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:o="http://schemas.microsoft.com/winfx/2006/xaml/presentation/options">

    <!-- ========================================
         Fluent Design Brushes
         基于Fluent Design颜色系统的画刷定义
         覆盖HandyControl默认画刷键值
         ======================================== -->

    <!-- 主色调画刷 (Primary Brushes) -->
    <SolidColorBrush o:Freeze="True" x:Key="LightPrimaryBrush" Color="{DynamicResource LightPrimaryColor}"/>
    <LinearGradientBrush o:Freeze="True" x:Key="PrimaryBrush" EndPoint="1,0" StartPoint="0,0">
        <GradientStop o:Freeze="True" Color="{DynamicResource PrimaryColor}" Offset="0"/>
        <GradientStop o:Freeze="True" Color="{DynamicResource DarkPrimaryColor}" Offset="1"/>
    </LinearGradientBrush>
    <SolidColorBrush o:Freeze="True" x:Key="DarkPrimaryBrush" Color="{DynamicResource DarkPrimaryColor}"/>

    <!-- 文本画刷 (Text Brushes) -->
    <SolidColorBrush o:Freeze="True" x:Key="PrimaryTextBrush" Color="{DynamicResource PrimaryTextColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="SecondaryTextBrush" Color="{DynamicResource SecondaryTextColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="ThirdlyTextBrush" Color="{DynamicResource ThirdlyTextColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="ReverseTextBrush" Color="{DynamicResource ReverseTextColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="TextIconBrush" Color="{DynamicResource TextIconColor}"/>

    <!-- 边框画刷 (Border Brushes) -->
    <SolidColorBrush o:Freeze="True" x:Key="BorderBrush" Color="{DynamicResource BorderColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="SecondaryBorderBrush" Color="{DynamicResource SecondaryBorderColor}"/>

    <!-- 背景画刷 (Background Brushes) -->
    <SolidColorBrush o:Freeze="True" x:Key="BackgroundBrush" Color="{DynamicResource BackgroundColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="RegionBrush" Color="{DynamicResource RegionColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="SecondaryRegionBrush" Color="{DynamicResource SecondaryRegionColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="ThirdlyRegionBrush" Color="{DynamicResource ThirdlyRegionColor}"/>

    <!-- 标题画刷 (Title Brushes) -->
    <LinearGradientBrush o:Freeze="True" x:Key="TitleBrush" EndPoint="1,0" StartPoint="0,0">
        <GradientStop o:Freeze="True" Color="{DynamicResource TitleColor}" Offset="0"/>
        <GradientStop o:Freeze="True" Color="{DynamicResource SecondaryTitleColor}" Offset="1"/>
    </LinearGradientBrush>

    <!-- 默认控件画刷 (Default Control Brushes) -->
    <SolidColorBrush o:Freeze="True" x:Key="DefaultBrush" Color="{DynamicResource DefaultColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="DarkDefaultBrush" Color="{DynamicResource DarkDefaultColor}"/>

    <!-- 语义画刷 (Semantic Brushes) -->
    <!-- 成功画刷 -->
    <SolidColorBrush o:Freeze="True" x:Key="LightSuccessBrush" Color="{DynamicResource LightSuccessColor}"/>
    <LinearGradientBrush o:Freeze="True" x:Key="SuccessBrush" EndPoint="1,0" StartPoint="0,0">
        <GradientStop o:Freeze="True" Color="{DynamicResource SuccessColor}" Offset="0"/>
        <GradientStop o:Freeze="True" Color="{DynamicResource DarkSuccessColor}" Offset="1"/>
    </LinearGradientBrush>
    <SolidColorBrush o:Freeze="True" x:Key="DarkSuccessBrush" Color="{DynamicResource DarkSuccessColor}"/>

    <!-- 警告画刷 -->
    <SolidColorBrush o:Freeze="True" x:Key="LightWarningBrush" Color="{DynamicResource LightWarningColor}"/>
    <LinearGradientBrush o:Freeze="True" x:Key="WarningBrush" EndPoint="1,0" StartPoint="0,0">
        <GradientStop o:Freeze="True" Color="{DynamicResource WarningColor}" Offset="0"/>
        <GradientStop o:Freeze="True" Color="{DynamicResource DarkWarningColor}" Offset="1"/>
    </LinearGradientBrush>
    <SolidColorBrush o:Freeze="True" x:Key="DarkWarningBrush" Color="{DynamicResource DarkWarningColor}"/>

    <!-- 错误画刷 -->
    <SolidColorBrush o:Freeze="True" x:Key="LightDangerBrush" Color="{DynamicResource LightDangerColor}"/>
    <LinearGradientBrush o:Freeze="True" x:Key="DangerBrush" EndPoint="1,0" StartPoint="0,0">
        <GradientStop o:Freeze="True" Color="{DynamicResource DangerColor}" Offset="0"/>
        <GradientStop o:Freeze="True" Color="{DynamicResource DarkDangerColor}" Offset="1"/>
    </LinearGradientBrush>
    <SolidColorBrush o:Freeze="True" x:Key="DarkDangerBrush" Color="{DynamicResource DarkDangerColor}"/>

    <!-- 信息画刷 -->
    <SolidColorBrush o:Freeze="True" x:Key="LightInfoBrush" Color="{DynamicResource LightInfoColor}"/>
    <LinearGradientBrush o:Freeze="True" x:Key="InfoBrush" EndPoint="1,0" StartPoint="0,0">
        <GradientStop o:Freeze="True" Color="{DynamicResource InfoColor}" Offset="0"/>
        <GradientStop o:Freeze="True" Color="{DynamicResource DarkInfoColor}" Offset="1"/>
    </LinearGradientBrush>
    <SolidColorBrush o:Freeze="True" x:Key="DarkInfoBrush" Color="{DynamicResource DarkInfoColor}"/>

    <!-- 强调画刷 (Accent Brushes) -->
    <SolidColorBrush o:Freeze="True" x:Key="AccentBrush" Color="{DynamicResource AccentColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="DarkAccentBrush" Color="{DynamicResource DarkAccentColor}"/>

    <!-- 遮罩和阴影画刷 (Mask and Shadow Brushes) -->
    <SolidColorBrush o:Freeze="True" x:Key="DarkMaskBrush" Color="{DynamicResource DarkMaskColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="DarkOpacityBrush" Color="{DynamicResource DarkOpacityColor}"/>

    <!-- 控件状态画刷 (Control State Brushes) -->
    <!-- 悬停状态画刷 -->
    <SolidColorBrush o:Freeze="True" x:Key="HoverBackgroundBrush" Color="{DynamicResource HoverBackgroundColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="HoverBorderBrush" Color="{DynamicResource HoverBorderColor}"/>
    
    <!-- 按下状态画刷 -->
    <SolidColorBrush o:Freeze="True" x:Key="PressedBackgroundBrush" Color="{DynamicResource PressedBackgroundColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="PressedBorderBrush" Color="{DynamicResource PressedBorderColor}"/>
    
    <!-- 禁用状态画刷 -->
    <SolidColorBrush o:Freeze="True" x:Key="DisabledBackgroundBrush" Color="{DynamicResource DisabledBackgroundColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="DisabledTextBrush" Color="{DynamicResource DisabledTextColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="DisabledBorderBrush" Color="{DynamicResource DisabledBorderColor}"/>
    
    <!-- 选中状态画刷 -->
    <SolidColorBrush o:Freeze="True" x:Key="SelectedBackgroundBrush" Color="{DynamicResource SelectedBackgroundColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="SelectedBorderBrush" Color="{DynamicResource SelectedBorderColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="SelectedTextBrush" Color="{DynamicResource SelectedTextColor}"/>
    
    <!-- 焦点状态画刷 -->
    <SolidColorBrush o:Freeze="True" x:Key="FocusBorderBrush" Color="{DynamicResource FocusBorderColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="FocusBackgroundBrush" Color="{DynamicResource FocusBackgroundColor}"/>

    <!-- 特殊效果画刷 (Special Effect Brushes) -->
    <!-- 毛玻璃效果背景 -->
    <LinearGradientBrush o:Freeze="True" x:Key="AcrylicBackgroundBrush" EndPoint="0,1" StartPoint="0,0" Opacity="0.8">
        <GradientStop o:Freeze="True" Color="{DynamicResource RegionColor}" Offset="0"/>
        <GradientStop o:Freeze="True" Color="{DynamicResource SecondaryRegionColor}" Offset="1"/>
    </LinearGradientBrush>
    
    <!-- 卡片阴影效果 -->
    <LinearGradientBrush o:Freeze="True" x:Key="CardShadowBrush" EndPoint="0,1" StartPoint="0,0">
        <GradientStop o:Freeze="True" Color="Transparent" Offset="0"/>
        <GradientStop o:Freeze="True" Color="{DynamicResource DarkMaskColor}" Offset="1"/>
    </LinearGradientBrush>

    <!-- 扩展的HandyControl兼容画刷 -->
    <!-- 为了更好地覆盖HandyControl的所有控件样式 -->

    <!-- 输入控件画刷 -->
    <SolidColorBrush o:Freeze="True" x:Key="TextBoxBackgroundBrush" Color="{DynamicResource RegionColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="TextBoxBorderBrush" Color="{DynamicResource BorderColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="TextBoxForegroundBrush" Color="{DynamicResource PrimaryTextColor}"/>

    <!-- 按钮画刷 -->
    <SolidColorBrush o:Freeze="True" x:Key="ButtonBackgroundBrush" Color="{DynamicResource DefaultColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="ButtonBorderBrush" Color="{DynamicResource BorderColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="ButtonForegroundBrush" Color="{DynamicResource PrimaryTextColor}"/>

    <!-- 列表控件画刷 -->
    <SolidColorBrush o:Freeze="True" x:Key="ListBoxBackgroundBrush" Color="{DynamicResource RegionColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="ListBoxItemBackgroundBrush" Color="Transparent"/>
    <SolidColorBrush o:Freeze="True" x:Key="ListBoxItemHoverBrush" Color="{DynamicResource HoverBackgroundColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="ListBoxItemSelectedBrush" Color="{DynamicResource SelectedBackgroundColor}"/>

</ResourceDictionary>
