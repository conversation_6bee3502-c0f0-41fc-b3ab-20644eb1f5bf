<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <!-- ========================================
         Fluent Design Dark Theme Colors
         深色主题配色方案 - 覆盖HandyControl默认颜色
         符合WCAG 2.1 AA级无障碍访问标准
         ======================================== -->

    <!-- 引用基础颜色定义 -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="FluentColors.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!-- 主色调映射 (Primary Colors) -->
    <Color x:Key="LightPrimaryColor">#113B17</Color>
    <Color x:Key="PrimaryColor">#2B88D8</Color>
    <Color x:Key="DarkPrimaryColor">#5BA0E7</Color>

    <!-- 文本颜色 (Text Colors) -->
    <!-- 主文本：浅色，确保与深色背景有足够对比度 (对比度 > 7:1) -->
    <Color x:Key="PrimaryTextColor">#FFFFFF</Color>
    <!-- 次要文本：中等浅色，用于辅助信息 (对比度 > 4.5:1) -->
    <Color x:Key="SecondaryTextColor">#C8C6C4</Color>
    <!-- 三级文本：较暗的浅色，用于占位符等 (对比度 > 3:1) -->
    <Color x:Key="ThirdlyTextColor">#797775</Color>
    <!-- 反向文本：用于浅色背景上的文本 -->
    <Color x:Key="ReverseTextColor">#161514</Color>
    <!-- 图标文本：用于图标和按钮文本 -->
    <Color x:Key="TextIconColor">#FFFFFF</Color>

    <!-- 背景颜色 (Background Colors) -->
    <!-- 主背景：深色背景 -->
    <Color x:Key="BackgroundColor">#161514</Color>
    <!-- 区域背景：用于卡片、面板等，比主背景稍亮 -->
    <Color x:Key="RegionColor">#1B1A19</Color>
    <!-- 次要区域背景：用于分组、侧边栏等 -->
    <Color x:Key="SecondaryRegionColor">#201F1E</Color>
    <!-- 三级区域背景：用于更深层次的分组 -->
    <Color x:Key="ThirdlyRegionColor">#252423</Color>

    <!-- 边框颜色 (Border Colors) -->
    <!-- 主边框：用于输入框、按钮等 -->
    <Color x:Key="BorderColor">#3B3A39</Color>
    <!-- 次要边框：用于分割线、卡片边框等 -->
    <Color x:Key="SecondaryBorderColor">#323130</Color>

    <!-- 标题颜色 (Title Colors) -->
    <Color x:Key="TitleColor">#2B88D8</Color>
    <Color x:Key="SecondaryTitleColor">#5BA0E7</Color>

    <!-- 默认控件颜色 (Default Control Colors) -->
    <Color x:Key="DefaultColor">#201F1E</Color>
    <Color x:Key="DarkDefaultColor">#1B1A19</Color>

    <!-- 语义颜色 (Semantic Colors) -->
    <!-- 成功色 -->
    <Color x:Key="LightSuccessColor">#113B17</Color>
    <Color x:Key="SuccessColor">#9FD89F</Color>
    <Color x:Key="DarkSuccessColor">#C7E6C7</Color>

    <!-- 警告色 -->
    <Color x:Key="LightWarningColor">#663E00</Color>
    <Color x:Key="WarningColor">#FFB366</Color>
    <Color x:Key="DarkWarningColor">#FFCC99</Color>

    <!-- 错误色 -->
    <Color x:Key="LightDangerColor">#661616</Color>
    <Color x:Key="DangerColor">#E6999B</Color>
    <Color x:Key="DarkDangerColor">#F0B3B5</Color>

    <!-- 信息色 -->
    <Color x:Key="LightInfoColor">#00414D</Color>
    <Color x:Key="InfoColor">#66C2DE</Color>
    <Color x:Key="DarkInfoColor">#99D5E8</Color>

    <!-- 强调色 (Accent Colors) -->
    <Color x:Key="AccentColor">#B7A0D8</Color>
    <Color x:Key="DarkAccentColor">#C3AFE0</Color>

    <!-- 遮罩和阴影颜色 (Mask and Shadow Colors) -->
    <Color x:Key="DarkMaskColor">#1F000000</Color>
    <Color x:Key="DarkOpacityColor">#33000000</Color>

    <!-- 特殊效果 -->
    <system:UInt32 x:Key="BlurGradientValue">0x99000000</system:UInt32>

    <!-- 控件状态颜色 (Control State Colors) -->
    <!-- 悬停状态 -->
    <Color x:Key="HoverBackgroundColor">#252423</Color>
    <Color x:Key="HoverBorderColor">#484644</Color>

    <!-- 按下状态 -->
    <Color x:Key="PressedBackgroundColor">#323130</Color>
    <Color x:Key="PressedBorderColor">#605E5C</Color>

    <!-- 禁用状态 -->
    <Color x:Key="DisabledBackgroundColor">#1B1A19</Color>
    <Color x:Key="DisabledTextColor">#3B3A39</Color>
    <Color x:Key="DisabledBorderColor">#252423</Color>

    <!-- 选中状态 -->
    <Color x:Key="SelectedBackgroundColor">#004E66</Color>
    <Color x:Key="SelectedBorderColor">#2B88D8</Color>
    <Color x:Key="SelectedTextColor">#8BB8F0</Color>

    <!-- 焦点状态 -->
    <Color x:Key="FocusBorderColor">#2B88D8</Color>
    <Color x:Key="FocusBackgroundColor">#113B17</Color>

</ResourceDictionary>
