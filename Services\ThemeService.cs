using System;
using System.Windows;

namespace AirMonitor.Services
{
    /// <summary>
    /// 主题服务 - 管理Fluent Design主题切换
    /// </summary>
    public class ThemeService
    {
        private const string FluentLightThemeUri = "Resources/Themes/FluentDesign/FluentColorsLight.xaml";
        private const string FluentDarkThemeUri = "Resources/Themes/FluentDesign/FluentColorsDark.xaml";
        
        /// <summary>
        /// 当前主题类型
        /// </summary>
        public enum ThemeType
        {
            Light,
            Dark
        }

        private ThemeType _currentTheme = ThemeType.Light;

        /// <summary>
        /// 当前主题
        /// </summary>
        public ThemeType CurrentTheme => _currentTheme;

        /// <summary>
        /// 主题变更事件
        /// </summary>
        public event EventHandler<ThemeType>? ThemeChanged;

        /// <summary>
        /// 切换到指定主题
        /// </summary>
        /// <param name="theme">目标主题</param>
        public void SwitchTheme(ThemeType theme)
        {
            if (_currentTheme == theme)
                return;

            try
            {
                var app = Application.Current;
                if (app?.Resources == null)
                    return;

                // 移除当前主题资源
                RemoveCurrentThemeResource(app.Resources);

                // 添加新主题资源
                AddThemeResource(app.Resources, theme);

                _currentTheme = theme;
                ThemeChanged?.Invoke(this, theme);
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常，保持应用程序稳定
                System.Diagnostics.Debug.WriteLine($"主题切换失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 切换主题（在浅色和深色之间切换）
        /// </summary>
        public void ToggleTheme()
        {
            var newTheme = _currentTheme == ThemeType.Light ? ThemeType.Dark : ThemeType.Light;
            SwitchTheme(newTheme);
        }

        /// <summary>
        /// 初始化主题服务
        /// </summary>
        /// <param name="initialTheme">初始主题</param>
        public void Initialize(ThemeType initialTheme = ThemeType.Light)
        {
            SwitchTheme(initialTheme);
        }

        /// <summary>
        /// 移除当前主题资源
        /// </summary>
        private void RemoveCurrentThemeResource(ResourceDictionary resources)
        {
            var lightThemeUri = new Uri(FluentLightThemeUri, UriKind.Relative);
            var darkThemeUri = new Uri(FluentDarkThemeUri, UriKind.Relative);

            // 查找并移除现有的主题资源
            for (int i = resources.MergedDictionaries.Count - 1; i >= 0; i--)
            {
                var dictionary = resources.MergedDictionaries[i];
                if (dictionary.Source != null && 
                    (dictionary.Source.Equals(lightThemeUri) || dictionary.Source.Equals(darkThemeUri)))
                {
                    resources.MergedDictionaries.RemoveAt(i);
                }
            }
        }

        /// <summary>
        /// 添加主题资源
        /// </summary>
        private void AddThemeResource(ResourceDictionary resources, ThemeType theme)
        {
            var themeUri = theme == ThemeType.Light ? FluentLightThemeUri : FluentDarkThemeUri;
            var themeResource = new ResourceDictionary
            {
                Source = new Uri(themeUri, UriKind.Relative)
            };

            // 将主题资源插入到合适的位置（在HandyControl主题之后，在画刷资源之前）
            var insertIndex = FindInsertIndex(resources);
            resources.MergedDictionaries.Insert(insertIndex, themeResource);
        }

        /// <summary>
        /// 查找插入主题资源的合适位置
        /// </summary>
        private int FindInsertIndex(ResourceDictionary resources)
        {
            // 查找FluentBrushes.xaml的位置，主题颜色应该在画刷之前加载
            for (int i = 0; i < resources.MergedDictionaries.Count; i++)
            {
                var dictionary = resources.MergedDictionaries[i];
                if (dictionary.Source != null && 
                    dictionary.Source.OriginalString.Contains("FluentBrushes.xaml"))
                {
                    return i; // 在画刷资源之前插入
                }
            }

            // 如果没找到画刷资源，插入到最后
            return resources.MergedDictionaries.Count;
        }

        /// <summary>
        /// 获取主题显示名称
        /// </summary>
        public string GetThemeDisplayName(ThemeType theme)
        {
            return theme switch
            {
                ThemeType.Light => "浅色主题",
                ThemeType.Dark => "深色主题",
                _ => "未知主题"
            };
        }

        /// <summary>
        /// 检查是否为深色主题
        /// </summary>
        public bool IsDarkTheme => _currentTheme == ThemeType.Dark;

        /// <summary>
        /// 检查是否为浅色主题
        /// </summary>
        public bool IsLightTheme => _currentTheme == ThemeType.Light;
    }
}
