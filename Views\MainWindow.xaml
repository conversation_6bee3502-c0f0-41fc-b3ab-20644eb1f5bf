<hc:Window
    x:Class="AirMonitor.Views.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:vm="clr-namespace:AirMonitor.ViewModels"
    Title="{Binding Title}"
    Width="900"
    Height="600"
    MinWidth="600"
    MinHeight="400"
    NonClientAreaBackground="{DynamicResource PrimaryBrush}"
    NonClientAreaForeground="White"
    ShowNonClientArea="True"
    WindowStartupLocation="CenterScreen">

    <!--  DataContext 将通过依赖注入在代码后置中设置  -->

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  菜单栏  -->
        <Menu Grid.Row="0">
            <MenuItem Header="文件(_F)">
                <MenuItem Command="{Binding ExitCommand}" Header="退出(_X)" />
            </MenuItem>
            <MenuItem Header="主题(_T)">
                <MenuItem Command="{Binding SwitchToLightThemeCommand}" Header="浅色主题(_L)" />
                <MenuItem Command="{Binding SwitchToDarkThemeCommand}" Header="深色主题(_D)" />
                <Separator />
                <MenuItem Command="{Binding ToggleThemeCommand}" Header="切换主题(_S)" />
            </MenuItem>
            <MenuItem Header="帮助(_H)">
                <MenuItem Command="{Binding AboutCommand}" Header="关于(_A)" />
            </MenuItem>
        </Menu>

        <!--  主内容区域  -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!--  应用程序信息  -->
            <hc:Card
                Grid.Row="0"
                Margin="0,0,0,20"
                Padding="20">
                <StackPanel>
                    <TextBlock
                        HorizontalAlignment="Center"
                        FontSize="24"
                        FontWeight="Bold"
                        Text="{Binding ApplicationName}" />
                    <TextBlock
                        Margin="0,5,0,0"
                        HorizontalAlignment="Center"
                        FontSize="14"
                        Text="{Binding ApplicationDescription}" />
                    <TextBlock
                        Margin="0,5,0,0"
                        HorizontalAlignment="Center"
                        FontSize="12"
                        Text="{Binding ApplicationVersion, StringFormat='版本: {0}'}" />
                </StackPanel>
            </hc:Card>

            <!--  主工作区域 - Fluent Design配色系统测试  -->
            <hc:ScrollViewer Grid.Row="1">
                <StackPanel Margin="10">

                    <!--  主题信息卡片  -->
                    <hc:Card Margin="0,0,0,15" Padding="20">
                        <StackPanel>
                            <TextBlock
                                HorizontalAlignment="Center"
                                FontSize="20"
                                FontWeight="Bold"
                                Foreground="{DynamicResource PrimaryTextBrush}"
                                Text="Fluent Design 配色系统测试" />
                            <TextBlock
                                Margin="0,10,0,0"
                                HorizontalAlignment="Center"
                                FontSize="12"
                                Foreground="{DynamicResource SecondaryTextBrush}"
                                Text="{Binding CurrentTime, StringFormat='当前时间: yyyy-MM-dd HH:mm:ss'}" />
                        </StackPanel>
                    </hc:Card>

                    <!--  按钮测试区域  -->
                    <hc:Card Margin="0,0,0,15" Padding="20">
                        <StackPanel>
                            <TextBlock
                                Margin="0,0,0,10"
                                FontSize="16"
                                FontWeight="Bold"
                                Text="按钮控件测试" />
                            <WrapPanel>
                                <Button
                                    Margin="5"
                                    Padding="10,5"
                                    Content="默认按钮" />
                                <Button
                                    Margin="5"
                                    Padding="10,5"
                                    Content="主要按钮"
                                    Style="{DynamicResource ButtonPrimary}" />
                                <Button
                                    Margin="5"
                                    Padding="10,5"
                                    Content="成功按钮"
                                    Style="{DynamicResource ButtonSuccess}" />
                                <Button
                                    Margin="5"
                                    Padding="10,5"
                                    Content="警告按钮"
                                    Style="{DynamicResource ButtonWarning}" />
                                <Button
                                    Margin="5"
                                    Padding="10,5"
                                    Content="危险按钮"
                                    Style="{DynamicResource ButtonDanger}" />
                                <Button
                                    Margin="5"
                                    Padding="10,5"
                                    Content="信息按钮"
                                    Style="{DynamicResource ButtonInfo}" />
                                <Button
                                    Margin="5"
                                    Padding="10,5"
                                    Content="禁用按钮"
                                    IsEnabled="False" />
                            </WrapPanel>
                        </StackPanel>
                    </hc:Card>

                    <!--  输入控件测试区域  -->
                    <hc:Card Margin="0,0,0,15" Padding="20">
                        <StackPanel>
                            <TextBlock
                                Margin="0,0,0,10"
                                FontSize="16"
                                FontWeight="Bold"
                                Text="输入控件测试" />
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                    <TextBox Margin="0,5" hc:InfoElement.Placeholder="请输入文本" />
                                    <PasswordBox Margin="0,5" hc:InfoElement.Placeholder="请输入密码" />
                                    <hc:NumericUpDown Margin="0,5" Value="100" />
                                    <DatePicker Margin="0,5" />
                                </StackPanel>
                                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                    <ComboBox Margin="0,5" hc:InfoElement.Placeholder="请选择选项">
                                        <ComboBoxItem Content="选项1" />
                                        <ComboBoxItem Content="选项2" />
                                        <ComboBoxItem Content="选项3" />
                                    </ComboBox>
                                    <hc:SearchBar Margin="0,5" hc:InfoElement.Placeholder="搜索..." />
                                    <CheckBox Margin="0,5" Content="复选框选项" />
                                    <RadioButton Margin="0,5" Content="单选按钮" />
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </hc:Card>

                </StackPanel>
            </hc:ScrollViewer>
        </Grid>

        <!--  状态栏  -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" />
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Text="{Binding CurrentTime, StringFormat=HH:mm:ss}" />
            </StatusBarItem>
        </StatusBar>
    </Grid>
</hc:Window>
