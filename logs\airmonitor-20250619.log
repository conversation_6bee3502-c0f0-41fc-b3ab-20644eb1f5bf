[2025-06-19 13:58:40.357 +08:00 INF] AirMonitor 应用程序启动完成
[2025-06-19 13:58:40.372 +08:00 INF] 配置文件加载完成
[2025-06-19 13:58:40.372 +08:00 INF] 日志系统配置完成，日志文件位置: D:\09 AirMonitor\logs
[2025-06-19 13:58:40.378 +08:00 INF] MainWindowViewModel 已创建
[2025-06-19 13:58:40.565 +08:00 INF] MainWindow 已创建
[2025-06-19 13:58:40.708 +08:00 INF] MainWindow 已初始化
[2025-06-19 13:58:59.445 +08:00 INF] 显示关于信息
[2025-06-19 13:59:01.111 +08:00 INF] 显示关于信息
[2025-06-19 13:59:02.112 +08:00 INF] 用户请求退出应用程序
[2025-06-19 13:59:02.131 +08:00 INF] MainWindowViewModel 清理完成
[2025-06-19 13:59:02.131 +08:00 INF] MainWindow 已关闭
[2025-06-19 13:59:02.134 +08:00 INF] 应用程序正在退出
[2025-06-19 14:38:13.081 +08:00 INF] AirMonitor 应用程序启动完成
[2025-06-19 14:38:13.097 +08:00 INF] 配置文件加载完成
[2025-06-19 14:38:13.098 +08:00 INF] 日志系统配置完成，日志文件位置: D:\09 AirMonitor\logs
[2025-06-19 14:38:13.104 +08:00 INF] MainWindowViewModel 已创建
[2025-06-19 14:38:13.368 +08:00 INF] MainWindow 已创建
[2025-06-19 14:38:13.507 +08:00 FTL] 应用程序启动失败
System.Windows.Markup.XamlParseException: “无法从文本“{StaticResource FluentGray120}”创建“Color”。”，行号为“45”，行位置为“6”。
 ---> System.FormatException: 令牌无效。
   at MS.Internal.Parsers.ParseColor(String color, IFormatProvider formatProvider, ITypeDescriptorContext context)
   at System.Windows.Media.ColorConverter.ConvertFrom(ITypeDescriptorContext td, CultureInfo ci, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CreateObjectWithTypeConverter(ServiceProviderContext serviceContext, XamlValueConverter`1 ts, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CreateFromValue(ServiceProviderContext serviceContext, XamlValueConverter`1 ts, Object value, XamlMember property)
   at System.Xaml.XamlObjectWriter.Logic_CreateFromValue(ObjectWriterContext ctx, XamlValueConverter`1 typeConverter, Object value, XamlMember property, String targetName, IAddLineInfo lineInfo)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.ResourceDictionary.CreateObject(KeyRecord key)
   at System.Windows.ResourceDictionary.OnGettingValue(Object key, Object& value, Boolean& canCache)
   at System.Windows.ResourceDictionary.OnGettingValuePrivate(Object key, Object& value, Boolean& canCache)
   at System.Windows.ResourceDictionary.GetValueWithoutLock(Object key, Boolean& canCache)
   at System.Windows.ResourceDictionary.GetValue(Object key, Boolean& canCache)
   at System.Windows.ResourceDictionary.GetValueWithoutLock(Object key, Boolean& canCache)
   at System.Windows.ResourceDictionary.GetValue(Object key, Boolean& canCache)
   at System.Windows.ResourceDictionary.GetValueWithoutLock(Object key, Boolean& canCache)
   at System.Windows.ResourceDictionary.GetValue(Object key, Boolean& canCache)
   at System.Windows.DeferredResourceReference.GetValue(BaseValueSourceInternal valueSource)
   at System.Windows.DeferredAppResourceReference.GetValue(BaseValueSourceInternal valueSource)
   at System.Windows.DependencyObject.GetEffectiveValue(EntryIndex entryIndex, DependencyProperty dp, RequestFlags requests)
   at System.Windows.DependencyObject.GetValueEntry(EntryIndex entryIndex, DependencyProperty dp, PropertyMetadata metadata, RequestFlags requests)
   at System.Windows.DependencyObject.GetValue(DependencyProperty dp)
   at System.Windows.Media.SolidColorBrush.get_Color()
   at System.Windows.Controls.Border.ArrangeOverride(Size finalSize)
   at System.Windows.FrameworkElement.ArrangeCore(Rect finalRect)
   at System.Windows.UIElement.Arrange(Rect finalRect)
   at System.Windows.Controls.Control.ArrangeOverride(Size arrangeBounds)
   at System.Windows.FrameworkElement.ArrangeCore(Rect finalRect)
   at System.Windows.UIElement.Arrange(Rect finalRect)
   at System.Windows.Controls.Grid.ArrangeOverride(Size arrangeSize)
   at System.Windows.FrameworkElement.ArrangeCore(Rect finalRect)
   at System.Windows.UIElement.Arrange(Rect finalRect)
   at System.Windows.Controls.Grid.ArrangeOverride(Size arrangeSize)
   at System.Windows.FrameworkElement.ArrangeCore(Rect finalRect)
   at System.Windows.UIElement.Arrange(Rect finalRect)
   at MS.Internal.Helper.ArrangeElementWithSingleChild(UIElement element, Size arrangeSize)
   at System.Windows.Controls.ContentPresenter.ArrangeOverride(Size arrangeSize)
   at System.Windows.FrameworkElement.ArrangeCore(Rect finalRect)
   at System.Windows.UIElement.Arrange(Rect finalRect)
   at System.Windows.Controls.Decorator.ArrangeOverride(Size arrangeSize)
   at System.Windows.Documents.AdornerDecorator.ArrangeOverride(Size finalSize)
   at System.Windows.FrameworkElement.ArrangeCore(Rect finalRect)
   at System.Windows.UIElement.Arrange(Rect finalRect)
   at System.Windows.Controls.Grid.ArrangeOverride(Size arrangeSize)
   at System.Windows.FrameworkElement.ArrangeCore(Rect finalRect)
   at System.Windows.UIElement.Arrange(Rect finalRect)
   at System.Windows.Controls.Border.ArrangeOverride(Size finalSize)
   at System.Windows.FrameworkElement.ArrangeCore(Rect finalRect)
   at System.Windows.UIElement.Arrange(Rect finalRect)
   at System.Windows.Window.ArrangeOverride(Size arrangeBounds)
   at System.Windows.FrameworkElement.ArrangeCore(Rect finalRect)
   at System.Windows.UIElement.Arrange(Rect finalRect)
   at System.Windows.Interop.HwndSource.SetLayoutSize()
   at System.Windows.Interop.HwndSource.set_RootVisualInternal(Visual value)
   at System.Windows.Interop.HwndSource.set_RootVisual(Visual value)
   at System.Windows.Window.SetRootVisual()
   at System.Windows.Window.SetRootVisualAndUpdateSTC()
   at System.Windows.Window.SetupInitialState(Double requestedTop, Double requestedLeft, Double requestedWidth, Double requestedHeight)
   at System.Windows.Window.CreateSourceWindow(Boolean duringShow)
   at System.Windows.Window.CreateSourceWindowDuringShow()
   at System.Windows.Window.SafeCreateWindowDuringShow()
   at System.Windows.Window.ShowHelper(Object booleanBox)
   at System.Windows.Window.Show()
   at AirMonitor.App.OnStartup(StartupEventArgs e) in D:\09 AirMonitor\App.xaml.cs:line 38
[2025-06-19 14:44:51.628 +08:00 INF] AirMonitor 应用程序启动完成
[2025-06-19 14:44:51.646 +08:00 INF] 配置文件加载完成
[2025-06-19 14:44:51.646 +08:00 INF] 日志系统配置完成，日志文件位置: D:\09 AirMonitor\logs
[2025-06-19 14:44:51.653 +08:00 INF] MainWindowViewModel 已创建
[2025-06-19 14:44:51.910 +08:00 INF] MainWindow 已创建
[2025-06-19 14:44:52.083 +08:00 INF] MainWindow 已初始化
[2025-06-19 14:45:42.165 +08:00 INF] MainWindowViewModel 清理完成
[2025-06-19 14:45:42.165 +08:00 INF] MainWindow 已关闭
[2025-06-19 14:45:42.171 +08:00 INF] 应用程序正在退出
