# Fluent Design 配色系统测试指南

## 🎯 测试目标
验证WPF应用程序中的Fluent Design配色系统是否正常工作，包括主题切换功能和各种控件的配色效果。

## ✅ 问题修复说明
**已修复的错误**: `XLS0433 类型 'Color' 的值无效: '{StaticResource FluentBlue50}'`

**修复方法**: 将所有颜色资源定义从使用`{StaticResource}`引用改为直接使用十六进制颜色值，因为XAML中的`Color`类型不支持资源引用。

## 🚀 测试步骤

### 1. 启动应用程序
```bash
dotnet run --project AirMonitor.csproj
```

### 2. 验证默认浅色主题
应用程序启动后，您应该看到：
- **窗口标题栏**: 蓝色渐变背景 (#0078D4)
- **主背景**: 浅灰色背景 (#FAF9F8)
- **卡片区域**: 白色背景 (#FFFFFF)
- **文本颜色**: 深灰色文本 (#161514)
- **按钮**: 各种语义颜色的按钮

### 3. 测试主题切换功能
通过菜单栏进行测试：

#### 切换到深色主题
1. 点击菜单栏 **"主题(T)"**
2. 选择 **"深色主题(D)"**
3. 观察界面变化：
   - 背景变为深色 (#161514)
   - 文本变为浅色 (#FFFFFF)
   - 卡片背景变为深灰色 (#1B1A19)

#### 切换回浅色主题
1. 点击菜单栏 **"主题(T)"**
2. 选择 **"浅色主题(L)"**
3. 界面应恢复到浅色配色

#### 使用切换功能
1. 点击菜单栏 **"主题(T)"**
2. 选择 **"切换主题(S)"**
3. 主题应在浅色和深色之间切换

### 4. 验证控件配色效果

#### 按钮测试
检查以下按钮的配色：
- **默认按钮**: 白色背景，深色文本
- **主要按钮**: 蓝色背景 (#0078D4)，白色文本
- **成功按钮**: 绿色背景 (#107C10)，白色文本
- **警告按钮**: 橙色背景 (#FF8C00)，白色文本
- **危险按钮**: 红色背景 (#D13438)，白色文本
- **信息按钮**: 青色背景 (#0099BC)，白色文本
- **禁用按钮**: 灰色背景，禁用状态

#### 输入控件测试
检查以下控件的配色：
- **文本框**: 白色背景，灰色边框
- **密码框**: 与文本框一致的样式
- **数字输入框**: HandyControl样式
- **日期选择器**: 标准WPF样式
- **下拉框**: 白色背景，选项列表
- **搜索框**: HandyControl特有样式
- **复选框**: 标准样式
- **单选按钮**: 标准样式

### 5. 验证状态栏
- **左侧**: 显示状态消息
- **右侧**: 显示当前时间
- **主题切换时**: 状态栏应显示主题切换消息

## 🎨 预期效果

### 浅色主题特征
- 主背景: 浅灰白色
- 文本: 深灰色，高对比度
- 卡片: 纯白色背景
- 按钮: 鲜明的语义色彩
- 边框: 浅灰色，清晰分界

### 深色主题特征
- 主背景: 深灰色
- 文本: 浅色，高对比度
- 卡片: 深灰色背景
- 按钮: 调整后的语义色彩
- 边框: 深色调，适合深色背景

## 🔍 故障排除

### 如果主题切换不工作
1. 检查控制台输出是否有错误
2. 确认ThemeService已正确注册
3. 验证资源文件路径是否正确

### 如果颜色显示异常
1. 确认所有XAML文件没有语法错误
2. 检查颜色值是否为有效的十六进制格式
3. 验证DynamicResource引用是否正确

### 如果应用程序无法启动
1. 检查构建输出是否有错误
2. 确认所有依赖项已正确安装
3. 验证资源文件是否存在

## 📊 测试检查清单

- [ ] 应用程序成功启动
- [ ] 默认显示浅色主题
- [ ] 可以切换到深色主题
- [ ] 可以切换回浅色主题
- [ ] 切换主题功能正常工作
- [ ] 所有按钮显示正确颜色
- [ ] 输入控件样式正确
- [ ] 文本对比度清晰可读
- [ ] 状态栏显示主题切换消息
- [ ] 没有控制台错误信息

## 🎉 成功标准
如果以上所有测试项目都通过，说明Fluent Design配色系统已成功集成到您的WPF应用程序中！

## 📞 技术支持
如果遇到任何问题，请检查：
1. `README_FluentDesign.md` - 详细的使用文档
2. 控制台输出 - 查看错误信息
3. 资源文件 - 确认文件完整性
