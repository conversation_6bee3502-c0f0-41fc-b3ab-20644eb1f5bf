<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <!-- ========================================
         Microsoft Fluent Design System Colors
         符合无障碍访问标准的配色方案
         ======================================== -->

    <!-- 主色调系统 (Primary Colors) -->
    <!-- 基于Microsoft Fluent Design的蓝色主色调 -->
    <Color x:Key="FluentBlue10">#001A2E</Color>
    <Color x:Key="FluentBlue20">#00344A</Color>
    <Color x:Key="FluentBlue30">#004E66</Color>
    <Color x:Key="FluentBlue40">#006882</Color>
    <Color x:Key="FluentBlue50">#00829E</Color>
    <Color x:Key="FluentBlue60">#0078D4</Color>  <!-- 主蓝色 -->
    <Color x:Key="FluentBlue70">#106EBE</Color>
    <Color x:Key="FluentBlue80">#2B88D8</Color>
    <Color x:Key="FluentBlue90">#5BA0E7</Color>
    <Color x:Key="FluentBlue100">#8BB8F0</Color>
    <Color x:Key="FluentBlue110">#BBD0F9</Color>
    <Color x:Key="FluentBlue120">#E1ECFC</Color>
    <Color x:Key="FluentBlue130">#F3F8FE</Color>

    <!-- 中性色系统 (Neutral Colors) -->
    <!-- 用于文本、边框、背景等 -->
    <Color x:Key="FluentGray10">#11100F</Color>
    <Color x:Key="FluentGray20">#161514</Color>
    <Color x:Key="FluentGray30">#1B1A19</Color>
    <Color x:Key="FluentGray40">#201F1E</Color>
    <Color x:Key="FluentGray50">#252423</Color>
    <Color x:Key="FluentGray60">#323130</Color>
    <Color x:Key="FluentGray70">#3B3A39</Color>
    <Color x:Key="FluentGray80">#484644</Color>
    <Color x:Key="FluentGray90">#605E5C</Color>
    <Color x:Key="FluentGray100">#797775</Color>
    <Color x:Key="FluentGray110">#8A8886</Color>
    <Color x:Key="FluentGray120">#C8C6C4</Color>
    <Color x:Key="FluentGray130">#EDEBE9</Color>
    <Color x:Key="FluentGray140">#F3F2F1</Color>
    <Color x:Key="FluentGray150">#FAF9F8</Color>
    <Color x:Key="FluentGray160">#FFFFFF</Color>

    <!-- 语义色系统 (Semantic Colors) -->
    <!-- 成功色 (Success/Green) -->
    <Color x:Key="FluentGreen10">#0B1B0F</Color>
    <Color x:Key="FluentGreen20">#0E2A13</Color>
    <Color x:Key="FluentGreen30">#113B17</Color>
    <Color x:Key="FluentGreen40">#144C1B</Color>
    <Color x:Key="FluentGreen50">#175D1F</Color>
    <Color x:Key="FluentGreen60">#107C10</Color>  <!-- 主绿色 -->
    <Color x:Key="FluentGreen70">#0E700E</Color>
    <Color x:Key="FluentGreen80">#0C640C</Color>
    <Color x:Key="FluentGreen90">#0A580A</Color>
    <Color x:Key="FluentGreen100">#084C08</Color>
    <Color x:Key="FluentGreen110">#9FD89F</Color>
    <Color x:Key="FluentGreen120">#C7E6C7</Color>
    <Color x:Key="FluentGreen130">#DFF6DD</Color>

    <!-- 警告色 (Warning/Orange) -->
    <Color x:Key="FluentOrange10">#2E1A00</Color>
    <Color x:Key="FluentOrange20">#4A2C00</Color>
    <Color x:Key="FluentOrange30">#663E00</Color>
    <Color x:Key="FluentOrange40">#825000</Color>
    <Color x:Key="FluentOrange50">#9E6200</Color>
    <Color x:Key="FluentOrange60">#FF8C00</Color>  <!-- 主橙色 -->
    <Color x:Key="FluentOrange70">#E67E00</Color>
    <Color x:Key="FluentOrange80">#CC7000</Color>
    <Color x:Key="FluentOrange90">#B36200</Color>
    <Color x:Key="FluentOrange100">#995400</Color>
    <Color x:Key="FluentOrange110">#FFB366</Color>
    <Color x:Key="FluentOrange120">#FFCC99</Color>
    <Color x:Key="FluentOrange130">#FFE6CC</Color>

    <!-- 错误色 (Error/Red) -->
    <Color x:Key="FluentRed10">#2E0A0A</Color>
    <Color x:Key="FluentRed20">#4A1010</Color>
    <Color x:Key="FluentRed30">#661616</Color>
    <Color x:Key="FluentRed40">#821C1C</Color>
    <Color x:Key="FluentRed50">#9E2222</Color>
    <Color x:Key="FluentRed60">#D13438</Color>  <!-- 主红色 -->
    <Color x:Key="FluentRed70">#BC2E32</Color>
    <Color x:Key="FluentRed80">#A7282C</Color>
    <Color x:Key="FluentRed90">#922226</Color>
    <Color x:Key="FluentRed100">#7D1C20</Color>
    <Color x:Key="FluentRed110">#E6999B</Color>
    <Color x:Key="FluentRed120">#F0B3B5</Color>
    <Color x:Key="FluentRed130">#F9CCCD</Color>

    <!-- 信息色 (Info/Cyan) -->
    <Color x:Key="FluentCyan10">#001B1F</Color>
    <Color x:Key="FluentCyan20">#002E36</Color>
    <Color x:Key="FluentCyan30">#00414D</Color>
    <Color x:Key="FluentCyan40">#005464</Color>
    <Color x:Key="FluentCyan50">#00677B</Color>
    <Color x:Key="FluentCyan60">#0099BC</Color>  <!-- 主青色 -->
    <Color x:Key="FluentCyan70">#0089A9</Color>
    <Color x:Key="FluentCyan80">#007996</Color>
    <Color x:Key="FluentCyan90">#006983</Color>
    <Color x:Key="FluentCyan100">#005970</Color>
    <Color x:Key="FluentCyan110">#66C2DE</Color>
    <Color x:Key="FluentCyan120">#99D5E8</Color>
    <Color x:Key="FluentCyan130">#CCE8F2</Color>

    <!-- 强调色系统 (Accent Colors) -->
    <!-- 紫色强调色 -->
    <Color x:Key="FluentPurple10">#1A0B2E</Color>
    <Color x:Key="FluentPurple20">#2C144A</Color>
    <Color x:Key="FluentPurple30">#3E1D66</Color>
    <Color x:Key="FluentPurple40">#502682</Color>
    <Color x:Key="FluentPurple50">#622F9E</Color>
    <Color x:Key="FluentPurple60">#8764B8</Color>  <!-- 主紫色 -->
    <Color x:Key="FluentPurple70">#9373C0</Color>
    <Color x:Key="FluentPurple80">#9F82C8</Color>
    <Color x:Key="FluentPurple90">#AB91D0</Color>
    <Color x:Key="FluentPurple100">#B7A0D8</Color>
    <Color x:Key="FluentPurple110">#C3AFE0</Color>
    <Color x:Key="FluentPurple120">#CFBEE8</Color>
    <Color x:Key="FluentPurple130">#DBCDF0</Color>

    <!-- 透明度系统 (Opacity System) -->
    <system:Double x:Key="FluentOpacity10">0.1</system:Double>
    <system:Double x:Key="FluentOpacity20">0.2</system:Double>
    <system:Double x:Key="FluentOpacity30">0.3</system:Double>
    <system:Double x:Key="FluentOpacity40">0.4</system:Double>
    <system:Double x:Key="FluentOpacity50">0.5</system:Double>
    <system:Double x:Key="FluentOpacity60">0.6</system:Double>
    <system:Double x:Key="FluentOpacity70">0.7</system:Double>
    <system:Double x:Key="FluentOpacity80">0.8</system:Double>
    <system:Double x:Key="FluentOpacity90">0.9</system:Double>

    <!-- 阴影和遮罩颜色 -->
    <Color x:Key="FluentShadowLight">#1A000000</Color>
    <Color x:Key="FluentShadowMedium">#33000000</Color>
    <Color x:Key="FluentShadowDark">#4D000000</Color>
    <Color x:Key="FluentMaskLight">#0F000000</Color>
    <Color x:Key="FluentMaskMedium">#1F000000</Color>
    <Color x:Key="FluentMaskDark">#2F000000</Color>

    <!-- 特殊效果颜色 -->
    <system:UInt32 x:Key="FluentBlurGradientValue">0x99FFFFFF</system:UInt32>
    <system:UInt32 x:Key="FluentBlurGradientValueDark">0x99000000</system:UInt32>

</ResourceDictionary>
