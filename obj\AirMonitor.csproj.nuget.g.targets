﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)system.text.encodings.web\9.0.6\buildTransitive\netcoreapp2.0\System.Text.Encodings.Web.targets" Condition="Exists('$(NuGetPackageRoot)system.text.encodings.web\9.0.6\buildTransitive\netcoreapp2.0\System.Text.Encodings.Web.targets')" />
    <Import Project="$(NuGetPackageRoot)system.io.pipelines\9.0.6\buildTransitive\netcoreapp2.0\System.IO.Pipelines.targets" Condition="Exists('$(NuGetPackageRoot)system.io.pipelines\9.0.6\buildTransitive\netcoreapp2.0\System.IO.Pipelines.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.bcl.asyncinterfaces\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Bcl.AsyncInterfaces.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.bcl.asyncinterfaces\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Bcl.AsyncInterfaces.targets')" />
    <Import Project="$(NuGetPackageRoot)system.text.json\9.0.6\buildTransitive\netcoreapp2.0\System.Text.Json.targets" Condition="Exists('$(NuGetPackageRoot)system.text.json\9.0.6\buildTransitive\netcoreapp2.0\System.Text.Json.targets')" />
    <Import Project="$(NuGetPackageRoot)system.diagnostics.eventlog\9.0.6\buildTransitive\netcoreapp2.0\System.Diagnostics.EventLog.targets" Condition="Exists('$(NuGetPackageRoot)system.diagnostics.eventlog\9.0.6\buildTransitive\netcoreapp2.0\System.Diagnostics.EventLog.targets')" />
    <Import Project="$(NuGetPackageRoot)system.diagnostics.diagnosticsource\9.0.6\buildTransitive\netcoreapp2.0\System.Diagnostics.DiagnosticSource.targets" Condition="Exists('$(NuGetPackageRoot)system.diagnostics.diagnosticsource\9.0.6\buildTransitive\netcoreapp2.0\System.Diagnostics.DiagnosticSource.targets')" />
    <Import Project="$(NuGetPackageRoot)serilog\4.3.0\build\Serilog.targets" Condition="Exists('$(NuGetPackageRoot)serilog\4.3.0\build\Serilog.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.primitives\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Primitives.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.primitives\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Primitives.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.dependencymodel\9.0.0\buildTransitive\netcoreapp2.0\Microsoft.Extensions.DependencyModel.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.dependencymodel\9.0.0\buildTransitive\netcoreapp2.0\Microsoft.Extensions.DependencyModel.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.abstractions\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.abstractions\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.binder\9.0.6\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.binder\9.0.6\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.dependencyinjection.abstractions\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.DependencyInjection.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.dependencyinjection.abstractions\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.DependencyInjection.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.options\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Options.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.options\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Options.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Logging.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.dependencyinjection\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.DependencyInjection.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.dependencyinjection\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.DependencyInjection.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Logging.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Logging.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.fileproviders.abstractions\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.FileProviders.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.fileproviders.abstractions\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.FileProviders.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.diagnostics.abstractions\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Diagnostics.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.diagnostics.abstractions\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Diagnostics.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.hosting.abstractions\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Hosting.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.hosting.abstractions\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Hosting.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.options.configurationextensions\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Options.ConfigurationExtensions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.options.configurationextensions\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Options.ConfigurationExtensions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.eventsource\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Logging.EventSource.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.eventsource\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Logging.EventSource.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.eventlog\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Logging.EventLog.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.eventlog\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Logging.EventLog.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.debug\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Logging.Debug.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.debug\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Logging.Debug.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.configuration\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Logging.Configuration.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.configuration\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Logging.Configuration.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.console\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Logging.Console.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.console\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Logging.Console.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.filesystemglobbing\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.FileSystemGlobbing.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.filesystemglobbing\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.FileSystemGlobbing.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.fileproviders.physical\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.FileProviders.Physical.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.fileproviders.physical\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.FileProviders.Physical.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.diagnostics\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Diagnostics.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.diagnostics\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Diagnostics.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.fileextensions\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.FileExtensions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.fileextensions\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.FileExtensions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.json\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.Json.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.json\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.Json.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.usersecrets\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.UserSecrets.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.usersecrets\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.UserSecrets.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.environmentvariables\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.EnvironmentVariables.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.environmentvariables\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.EnvironmentVariables.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.commandline\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.CommandLine.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.commandline\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.CommandLine.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.hosting\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Hosting.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.hosting\9.0.6\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Hosting.targets')" />
    <Import Project="$(NuGetPackageRoot)communitytoolkit.mvvm\8.4.0\buildTransitive\CommunityToolkit.Mvvm.targets" Condition="Exists('$(NuGetPackageRoot)communitytoolkit.mvvm\8.4.0\buildTransitive\CommunityToolkit.Mvvm.targets')" />
  </ImportGroup>
</Project>