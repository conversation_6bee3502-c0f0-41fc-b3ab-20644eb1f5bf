using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using System;
using System.IO;
using System.Windows;
using AirMonitor.ViewModels;
using AirMonitor.Views;

namespace AirMonitor
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;

        protected override void OnStartup(StartupEventArgs e)
        {
            // 先配置基础的 Serilog
            ConfigureBasicSerilog();

            try
            {
                // 配置和构建主机
                _host = CreateHostBuilder().Build();

                // 启动主机
                _host.Start();

                // 重新配置 Serilog 使用完整配置
                ConfigureSerilog();

                // 获取主窗口并显示
                var mainWindow = _host.Services.GetRequiredService<MainWindow>();
                mainWindow.Show();

                base.OnStartup(e);
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "应用程序启动失败");
                MessageBox.Show($"应用程序启动失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Current.Shutdown(1);
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            Log.Information("应用程序正在退出");
            _host?.Dispose();
            Log.CloseAndFlush();
            base.OnExit(e);
        }

        private static IHostBuilder CreateHostBuilder()
        {
            return Host.CreateDefaultBuilder()
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.SetBasePath(Directory.GetCurrentDirectory());
                    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                })
                .ConfigureServices((context, services) =>
                {
                    // 注册配置
                    services.Configure<ApplicationSettings>(
                        context.Configuration.GetSection("Application"));

                    // 注册 ViewModels
                    services.AddTransient<MainWindowViewModel>();

                    // 注册 Views
                    services.AddTransient<MainWindow>();

                    // 注册日志服务
                    services.AddLogging();

                    // 注册服务
                    services.AddSingleton<AirMonitor.Services.ThemeService>();
                })
                .UseSerilog();
        }

        private void ConfigureBasicSerilog()
        {
            // 创建基础的日志配置，用于应用程序启动阶段
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Information()
                .WriteTo.Console()
                .CreateLogger();

            Log.Information("AirMonitor 应用程序正在启动...");
        }

        private void ConfigureSerilog()
        {
            var configuration = _host!.Services.GetRequiredService<IConfiguration>();

            // 确保日志目录存在
            var logDirectory = Path.Combine(Directory.GetCurrentDirectory(), "logs");
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }

            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(configuration)
                .CreateLogger();

            Log.Information("AirMonitor 应用程序启动完成");
            Log.Information("配置文件加载完成");
            Log.Information("日志系统配置完成，日志文件位置: {LogDirectory}", logDirectory);
        }
    }

    /// <summary>
    /// 应用程序配置设置
    /// </summary>
    public class ApplicationSettings
    {
        public string Name { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }
}
