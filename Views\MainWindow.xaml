<hc:Window x:Class="AirMonitor.Views.MainWindow"
           xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
           xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
           xmlns:hc="https://handyorg.github.io/handycontrol"
           xmlns:vm="clr-namespace:AirMonitor.ViewModels"
           Title="{Binding Title}"
           Height="600" 
           Width="900"
           MinHeight="400"
           MinWidth="600"
           WindowStartupLocation="CenterScreen"
           ShowNonClientArea="True"
           NonClientAreaBackground="{DynamicResource PrimaryBrush}"
           NonClientAreaForeground="White">

    <!-- DataContext 将通过依赖注入在代码后置中设置 -->

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 菜单栏 -->
        <Menu Grid.Row="0">
            <MenuItem Header="文件(_F)">
                <MenuItem Header="退出(_X)" Command="{Binding ExitCommand}"/>
            </MenuItem>
            <MenuItem Header="帮助(_H)">
                <MenuItem Header="关于(_A)" Command="{Binding AboutCommand}"/>
            </MenuItem>
        </Menu>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 应用程序信息 -->
            <hc:Card Grid.Row="0" Margin="0,0,0,20" Padding="20">
                <StackPanel>
                    <TextBlock Text="{Binding ApplicationName}"
                               FontSize="24"
                               FontWeight="Bold"
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding ApplicationDescription}"
                               FontSize="14"
                               HorizontalAlignment="Center"
                               Margin="0,5,0,0"/>
                    <TextBlock Text="{Binding ApplicationVersion, StringFormat='版本: {0}'}"
                               FontSize="12"
                               HorizontalAlignment="Center"
                               Margin="0,5,0,0"/>
                </StackPanel>
            </hc:Card>

            <!-- 主工作区域 -->
            <hc:Card Grid.Row="1" Padding="20">
                <Grid>
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <hc:LoadingCircle Width="50" Height="50" Visibility="Collapsed"/>
                        <TextBlock Text="应用程序基础架构已就绪"
                                   FontSize="16"
                                   HorizontalAlignment="Center"
                                   Margin="0,20,0,0"/>
                        <TextBlock Text="等待业务功能模块集成..."
                                   FontSize="12"
                                   HorizontalAlignment="Center"
                                   Margin="0,10,0,0"/>

                        <!-- 当前时间显示 -->
                        <TextBlock Text="{Binding CurrentTime, StringFormat='当前时间: yyyy-MM-dd HH:mm:ss'}"
                                   FontSize="12"
                                   HorizontalAlignment="Center"
                                   Margin="0,20,0,0"/>
                    </StackPanel>
                </Grid>
            </hc:Card>
        </Grid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Text="{Binding CurrentTime, StringFormat=HH:mm:ss}"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</hc:Window>
