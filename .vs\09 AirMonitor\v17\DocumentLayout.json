{"Version": 1, "WorkspaceRootPath": "D:\\09 AirMonitor\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|d:\\09 airmonitor\\views\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|solutionrelative:views\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|d:\\09 airmonitor\\resources\\themes\\fluentdesign\\fluentcolorslight.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|solutionrelative:resources\\themes\\fluentdesign\\fluentcolorslight.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|d:\\09 airmonitor\\viewmodels\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|solutionrelative:viewmodels\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|d:\\09 airmonitor\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|solutionrelative:appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|d:\\09 airmonitor\\services\\themeservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|solutionrelative:services\\themeservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|d:\\09 airmonitor\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|solutionrelative:app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|d:\\09 airmonitor\\resources\\themes\\fluentdesign\\fluentbrushes.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|solutionrelative:resources\\themes\\fluentdesign\\fluentbrushes.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|d:\\09 airmonitor\\resources\\themes\\basic\\colors\\colors.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|solutionrelative:resources\\themes\\basic\\colors\\colors.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|d:\\09 airmonitor\\resources\\themes\\fluentdesign\\fluentcolors.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{CF1C4234-F4DE-69B0-EE8D-5FC86DB27AB2}|AirMonitor.csproj|solutionrelative:resources\\themes\\fluentdesign\\fluentcolors.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 10, "Children": [{"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:135:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d84ee353-0bef-5a41-a649-8f89aca5d84d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "MainWindowViewModel.cs", "DocumentMoniker": "D:\\09 AirMonitor\\ViewModels\\MainWindowViewModel.cs", "RelativeDocumentMoniker": "ViewModels\\MainWindowViewModel.cs", "ToolTip": "D:\\09 AirMonitor\\ViewModels\\MainWindowViewModel.cs", "RelativeToolTip": "ViewModels\\MainWindowViewModel.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAwAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T06:35:47.256Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "appsettings.json", "DocumentMoniker": "D:\\09 AirMonitor\\appsettings.json", "RelativeDocumentMoniker": "appsettings.json", "ToolTip": "D:\\09 AirMonitor\\appsettings.json", "RelativeToolTip": "appsettings.json", "ViewState": "AgIAAA8AAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-19T06:34:24.461Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\09 AirMonitor\\Views\\MainWindow.xaml", "RelativeDocumentMoniker": "Views\\MainWindow.xaml", "ToolTip": "D:\\09 AirMonitor\\Views\\MainWindow.xaml", "RelativeToolTip": "Views\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T06:33:41.787Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "ThemeService.cs", "DocumentMoniker": "D:\\09 AirMonitor\\Services\\ThemeService.cs", "RelativeDocumentMoniker": "Services\\ThemeService.cs", "ToolTip": "D:\\09 AirMonitor\\Services\\ThemeService.cs", "RelativeToolTip": "Services\\ThemeService.cs", "ViewState": "AgIAAJUAAAAAAAAAAAAAwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T06:33:24.356Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "App.xaml", "DocumentMoniker": "D:\\09 AirMonitor\\App.xaml", "RelativeDocumentMoniker": "App.xaml", "ToolTip": "D:\\09 AirMonitor\\App.xaml", "RelativeToolTip": "App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T06:32:45.347Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "FluentBrushes.xaml", "DocumentMoniker": "D:\\09 AirMonitor\\Resources\\Themes\\FluentDesign\\FluentBrushes.xaml", "RelativeDocumentMoniker": "Resources\\Themes\\FluentDesign\\FluentBrushes.xaml", "ToolTip": "D:\\09 AirMonitor\\Resources\\Themes\\FluentDesign\\FluentBrushes.xaml", "RelativeToolTip": "Resources\\Themes\\FluentDesign\\FluentBrushes.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T06:32:28.297Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "FluentColorsLight.xaml", "DocumentMoniker": "D:\\09 AirMonitor\\Resources\\Themes\\FluentDesign\\FluentColorsLight.xaml", "RelativeDocumentMoniker": "Resources\\Themes\\FluentDesign\\FluentColorsLight.xaml", "ToolTip": "D:\\09 AirMonitor\\Resources\\Themes\\FluentDesign\\FluentColorsLight.xaml", "RelativeToolTip": "Resources\\Themes\\FluentDesign\\FluentColorsLight.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T06:30:20.132Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "Colors.xaml", "DocumentMoniker": "D:\\09 AirMonitor\\Resources\\Themes\\Basic\\Colors\\Colors.xaml", "RelativeDocumentMoniker": "Resources\\Themes\\Basic\\Colors\\Colors.xaml", "ToolTip": "D:\\09 AirMonitor\\Resources\\Themes\\Basic\\Colors\\Colors.xaml", "RelativeToolTip": "Resources\\Themes\\Basic\\Colors\\Colors.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T06:31:05.292Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "FluentColors.xaml", "DocumentMoniker": "D:\\09 AirMonitor\\Resources\\Themes\\FluentDesign\\FluentColors.xaml", "RelativeDocumentMoniker": "Resources\\Themes\\FluentDesign\\FluentColors.xaml", "ToolTip": "D:\\09 AirMonitor\\Resources\\Themes\\FluentDesign\\FluentColors.xaml", "RelativeToolTip": "Resources\\Themes\\FluentDesign\\FluentColors.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T06:29:54.244Z", "EditorCaption": ""}]}]}]}