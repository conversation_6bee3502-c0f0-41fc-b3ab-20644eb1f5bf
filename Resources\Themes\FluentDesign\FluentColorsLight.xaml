<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <!--
        ========================================
        Fluent Design Light Theme Colors
        浅色主题配色方案 - 覆盖HandyControl默认颜色
        符合WCAG 2.1 AA级无障碍访问标准
        ========================================
    -->

    <!--  引用基础颜色定义  -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="FluentColors.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <!--  主色调映射 (Primary Colors)  -->
    <Color x:Key="LightPrimaryColor">{StaticResource FluentBlue130}</Color>
    <Color x:Key="PrimaryColor">{StaticResource FluentBlue60}</Color>
    <Color x:Key="DarkPrimaryColor">{StaticResource FluentBlue50}</Color>

    <!--  文本颜色 (Text Colors)  -->
    <!--  主文本：深灰色，确保与白色背景有足够对比度 (对比度 > 7:1)  -->
    <Color x:Key="PrimaryTextColor">{StaticResource FluentGray20}</Color>
    <!--  次要文本：中等灰色，用于辅助信息 (对比度 > 4.5:1)  -->
    <Color x:Key="SecondaryTextColor">{StaticResource FluentGray90}</Color>
    <!--  三级文本：浅灰色，用于占位符等 (对比度 > 3:1)  -->
    <Color x:Key="ThirdlyTextColor">{StaticResource FluentGray110}</Color>
    <!--  反向文本：用于深色背景上的文本  -->
    <Color x:Key="ReverseTextColor">{StaticResource FluentGray160}</Color>
    <!--  图标文本：用于图标和按钮文本  -->
    <Color x:Key="TextIconColor">{StaticResource FluentGray160}</Color>

    <!--  背景颜色 (Background Colors)  -->
    <!--  主背景：纯白色或接近白色  -->
    <Color x:Key="BackgroundColor">{StaticResource FluentGray150}</Color>
    <!--  区域背景：用于卡片、面板等  -->
    <Color x:Key="RegionColor">{StaticResource FluentGray160}</Color>
    <!--  次要区域背景：用于分组、侧边栏等  -->
    <Color x:Key="SecondaryRegionColor">{StaticResource FluentGray140}</Color>
    <!--  三级区域背景：用于更深层次的分组  -->
    <Color x:Key="ThirdlyRegionColor">{StaticResource FluentGray150}</Color>

    <!--  边框颜色 (Border Colors)  -->
    <!--  主边框：用于输入框、按钮等  -->
    <Color x:Key="BorderColor">{StaticResource FluentGray120}</Color>
    <!--  次要边框：用于分割线、卡片边框等  -->
    <Color x:Key="SecondaryBorderColor">{StaticResource FluentGray130}</Color>

    <!--  标题颜色 (Title Colors)  -->
    <Color x:Key="TitleColor">{StaticResource FluentBlue60}</Color>
    <Color x:Key="SecondaryTitleColor">{StaticResource FluentBlue70}</Color>

    <!--  默认控件颜色 (Default Control Colors)  -->
    <Color x:Key="DefaultColor">{StaticResource FluentGray160}</Color>
    <Color x:Key="DarkDefaultColor">{StaticResource FluentGray140}</Color>

    <!--  语义颜色 (Semantic Colors)  -->
    <!--  成功色  -->
    <Color x:Key="LightSuccessColor">{StaticResource FluentGreen130}</Color>
    <Color x:Key="SuccessColor">{StaticResource FluentGreen60}</Color>
    <Color x:Key="DarkSuccessColor">{StaticResource FluentGreen50}</Color>

    <!--  警告色  -->
    <Color x:Key="LightWarningColor">{StaticResource FluentOrange130}</Color>
    <Color x:Key="WarningColor">{StaticResource FluentOrange60}</Color>
    <Color x:Key="DarkWarningColor">{StaticResource FluentOrange50}</Color>

    <!--  错误色  -->
    <Color x:Key="LightDangerColor">{StaticResource FluentRed130}</Color>
    <Color x:Key="DangerColor">{StaticResource FluentRed60}</Color>
    <Color x:Key="DarkDangerColor">{StaticResource FluentRed50}</Color>

    <!--  信息色  -->
    <Color x:Key="LightInfoColor">{StaticResource FluentCyan130}</Color>
    <Color x:Key="InfoColor">{StaticResource FluentCyan60}</Color>
    <Color x:Key="DarkInfoColor">{StaticResource FluentCyan50}</Color>

    <!--  强调色 (Accent Colors)  -->
    <Color x:Key="AccentColor">{StaticResource FluentPurple60}</Color>
    <Color x:Key="DarkAccentColor">{StaticResource FluentPurple50}</Color>

    <!--  遮罩和阴影颜色 (Mask and Shadow Colors)  -->
    <Color x:Key="DarkMaskColor">{StaticResource FluentMaskLight}</Color>
    <Color x:Key="DarkOpacityColor">{StaticResource FluentShadowLight}</Color>

    <!--  特殊效果  -->
    <system:UInt32 x:Key="BlurGradientValue">{StaticResource FluentBlurGradientValue}</system:UInt32>

    <!--  控件状态颜色 (Control State Colors)  -->
    <!--  悬停状态  -->
    <Color x:Key="HoverBackgroundColor">{StaticResource FluentGray130}</Color>
    <Color x:Key="HoverBorderColor">{StaticResource FluentGray110}</Color>

    <!--  按下状态  -->
    <Color x:Key="PressedBackgroundColor">{StaticResource FluentGray120}</Color>
    <Color x:Key="PressedBorderColor">{StaticResource FluentGray100}</Color>

    <!--  禁用状态  -->
    <Color x:Key="DisabledBackgroundColor">{StaticResource FluentGray140}</Color>
    <Color x:Key="DisabledTextColor">{StaticResource FluentGray120}</Color>
    <Color x:Key="DisabledBorderColor">{StaticResource FluentGray130}</Color>

    <!--  选中状态  -->
    <Color x:Key="SelectedBackgroundColor">{StaticResource FluentBlue120}</Color>
    <Color x:Key="SelectedBorderColor">{StaticResource FluentBlue60}</Color>
    <Color x:Key="SelectedTextColor">{StaticResource FluentBlue40}</Color>

    <!--  焦点状态  -->
    <Color x:Key="FocusBorderColor">{StaticResource FluentBlue60}</Color>
    <Color x:Key="FocusBackgroundColor">{StaticResource FluentBlue130}</Color>

</ResourceDictionary>
