# WPF Fluent Design 配色系统

本项目为WPF应用程序实现了完整的Microsoft Fluent Design System配色方案，覆盖HandyControl的默认配色，提供现代化、一致性的用户界面体验。

## 🎨 功能特性

### 1. 完整的Fluent Design配色体系
- **主色调系统**: 基于Microsoft Fluent Design的蓝色主色调，包含13个层次的颜色变化
- **中性色系统**: 16个层次的灰色系统，用于文本、边框、背景等
- **语义色系统**: 成功(绿色)、警告(橙色)、错误(红色)、信息(青色)四套完整的语义色彩
- **强调色系统**: 紫色强调色，用于特殊强调和装饰

### 2. 双主题支持
- **浅色主题**: 适合日间使用，提供清晰明亮的视觉体验
- **深色主题**: 适合夜间使用，减少眼部疲劳
- **动态切换**: 支持运行时无缝切换主题

### 3. 无障碍访问标准
- 所有颜色组合均符合WCAG 2.1 AA级对比度要求
- 主文本对比度 > 7:1
- 次要文本对比度 > 4.5:1
- 三级文本对比度 > 3:1

### 4. HandyControl完美兼容
- 覆盖HandyControl所有默认颜色键值
- 保持控件功能不变，仅改变视觉外观
- 支持所有控件状态（正常、悬停、按下、禁用、选中、焦点）

## 📁 文件结构

```
Resources/Themes/FluentDesign/
├── FluentColors.xaml          # 基础颜色定义
├── FluentColorsLight.xaml     # 浅色主题配色
├── FluentColorsDark.xaml      # 深色主题配色
├── FluentBrushes.xaml         # 画刷资源定义
└── FluentTheme.xaml           # 主题管理器

Services/
└── ThemeService.cs            # 主题切换服务
```

## 🚀 使用方法

### 1. 基本集成

在`App.xaml`中引用Fluent Design主题：

```xml
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <!-- HandyControl 基础主题资源 -->
            <hc:Theme/>
            
            <!-- Fluent Design 配色系统 -->
            <ResourceDictionary Source="Resources/Themes/FluentDesign/FluentTheme.xaml"/>
        </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
</Application.Resources>
```

### 2. 主题切换服务

在依赖注入容器中注册主题服务：

```csharp
services.AddSingleton<ThemeService>();
```

在ViewModel中使用主题切换：

```csharp
public class MainWindowViewModel : ViewModelBase
{
    private readonly ThemeService _themeService;

    public MainWindowViewModel(ThemeService themeService)
    {
        _themeService = themeService;
        
        // 订阅主题变更事件
        _themeService.ThemeChanged += OnThemeChanged;
    }

    // 切换到浅色主题
    private void ExecuteSwitchToLightTheme()
    {
        _themeService.SwitchTheme(ThemeService.ThemeType.Light);
    }

    // 切换到深色主题
    private void ExecuteSwitchToDarkTheme()
    {
        _themeService.SwitchTheme(ThemeService.ThemeType.Dark);
    }

    // 在浅色和深色主题之间切换
    private void ExecuteToggleTheme()
    {
        _themeService.ToggleTheme();
    }
}
```

### 3. 在XAML中使用颜色资源

```xml
<!-- 使用主色调 -->
<Button Background="{DynamicResource PrimaryBrush}" 
        Foreground="{DynamicResource TextIconBrush}"/>

<!-- 使用语义色 -->
<Button Background="{DynamicResource SuccessBrush}" Content="成功"/>
<Button Background="{DynamicResource WarningBrush}" Content="警告"/>
<Button Background="{DynamicResource DangerBrush}" Content="危险"/>
<Button Background="{DynamicResource InfoBrush}" Content="信息"/>

<!-- 使用文本颜色 -->
<TextBlock Foreground="{DynamicResource PrimaryTextBrush}"/>
<TextBlock Foreground="{DynamicResource SecondaryTextBrush}"/>

<!-- 使用背景颜色 -->
<Grid Background="{DynamicResource BackgroundBrush}"/>
<Border Background="{DynamicResource RegionBrush}"/>
```

## 🎯 主要颜色资源

### 主色调画刷
- `PrimaryBrush` - 主色调渐变画刷
- `LightPrimaryBrush` - 浅色主色调
- `DarkPrimaryBrush` - 深色主色调

### 文本画刷
- `PrimaryTextBrush` - 主文本颜色
- `SecondaryTextBrush` - 次要文本颜色
- `ThirdlyTextBrush` - 三级文本颜色
- `ReverseTextBrush` - 反向文本颜色
- `TextIconBrush` - 图标文本颜色

### 背景画刷
- `BackgroundBrush` - 主背景颜色
- `RegionBrush` - 区域背景颜色
- `SecondaryRegionBrush` - 次要区域背景
- `ThirdlyRegionBrush` - 三级区域背景

### 边框画刷
- `BorderBrush` - 主边框颜色
- `SecondaryBorderBrush` - 次要边框颜色

### 语义画刷
- `SuccessBrush` / `LightSuccessBrush` / `DarkSuccessBrush` - 成功色
- `WarningBrush` / `LightWarningBrush` / `DarkWarningBrush` - 警告色
- `DangerBrush` / `LightDangerBrush` / `DarkDangerBrush` - 危险色
- `InfoBrush` / `LightInfoBrush` / `DarkInfoBrush` - 信息色

### 控件状态画刷
- `HoverBackgroundBrush` / `HoverBorderBrush` - 悬停状态
- `PressedBackgroundBrush` / `PressedBorderBrush` - 按下状态
- `DisabledBackgroundBrush` / `DisabledTextBrush` / `DisabledBorderBrush` - 禁用状态
- `SelectedBackgroundBrush` / `SelectedBorderBrush` / `SelectedTextBrush` - 选中状态
- `FocusBorderBrush` / `FocusBackgroundBrush` - 焦点状态

## 🔧 自定义扩展

### 添加新的颜色
在`FluentColors.xaml`中添加新的颜色定义：

```xml
<Color x:Key="MyCustomColor">#FF6B46C1</Color>
```

在`FluentBrushes.xaml`中创建对应的画刷：

```xml
<SolidColorBrush x:Key="MyCustomBrush" Color="{DynamicResource MyCustomColor}"/>
```

### 创建自定义主题
复制`FluentColorsLight.xaml`或`FluentColorsDark.xaml`，修改颜色映射来创建自定义主题。

## 📋 测试验证

应用程序包含完整的测试界面，展示各种HandyControl控件在新配色系统下的显示效果：

1. **按钮控件测试** - 展示不同类型按钮的配色效果
2. **输入控件测试** - 验证文本框、下拉框等输入控件的配色
3. **主题切换功能** - 通过菜单栏可以实时切换主题

## 🎨 设计原则

1. **一致性** - 所有颜色都基于统一的设计系统
2. **可访问性** - 符合无障碍访问标准
3. **可维护性** - 清晰的命名规范和文件组织
4. **可扩展性** - 易于添加新颜色和主题
5. **兼容性** - 与HandyControl完美兼容

## 📝 注意事项

1. 使用`DynamicResource`而不是`StaticResource`来引用颜色资源，以支持主题动态切换
2. 主题切换后，所有使用动态资源的控件会自动更新颜色
3. 自定义控件样式时，建议使用本配色系统提供的颜色资源
4. 在深色主题下，某些颜色会自动调整以保持良好的对比度

## 🔄 版本历史

- **v1.0.0** - 初始版本，实现完整的Fluent Design配色系统
  - 支持浅色和深色主题
  - 覆盖HandyControl默认配色
  - 提供主题切换服务
  - 符合无障碍访问标准
