﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <sys:Double x:Key="DefaultControlHeight">28</sys:Double>
    <sys:Double x:Key="SmallControlHeight">20</sys:Double>
    <Thickness x:Key="DefaultControlPadding">10,5</Thickness>
    <Thickness x:Key="DefaultInputPadding">8,0</Thickness>
    <CornerRadius x:Key="DefaultCornerRadius">4</CornerRadius>
    <sys:Double x:Key="{x:Static SystemParameters.VerticalScrollBarButtonHeightKey}">30</sys:Double>

</ResourceDictionary>
