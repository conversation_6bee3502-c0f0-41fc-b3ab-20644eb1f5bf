<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <!--
        ========================================
        Fluent Design Light Theme Colors
        浅色主题配色方案 - 覆盖HandyControl默认颜色
        符合WCAG 2.1 AA级无障碍访问标准
        ========================================
    -->

    <!--  引用基础颜色定义  -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="FluentColors.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <!--  主色调映射 (Primary Colors)  -->
    <Color x:Key="LightPrimaryColor">#F3F8FE</Color>
    <Color x:Key="PrimaryColor">#0078D4</Color>
    <Color x:Key="DarkPrimaryColor">#00829E</Color>

    <!--  文本颜色 (Text Colors)  -->
    <!--  主文本：深灰色，确保与白色背景有足够对比度 (对比度 > 7:1)  -->
    <Color x:Key="PrimaryTextColor">#161514</Color>
    <!--  次要文本：中等灰色，用于辅助信息 (对比度 > 4.5:1)  -->
    <Color x:Key="SecondaryTextColor">#605E5C</Color>
    <!--  三级文本：浅灰色，用于占位符等 (对比度 > 3:1)  -->
    <Color x:Key="ThirdlyTextColor">#8A8886</Color>
    <!--  反向文本：用于深色背景上的文本  -->
    <Color x:Key="ReverseTextColor">#FFFFFF</Color>
    <!--  图标文本：用于图标和按钮文本  -->
    <Color x:Key="TextIconColor">#FFFFFF</Color>

    <!--  背景颜色 (Background Colors)  -->
    <!--  主背景：纯白色或接近白色  -->
    <Color x:Key="BackgroundColor">#FAF9F8</Color>
    <!--  区域背景：用于卡片、面板等  -->
    <Color x:Key="RegionColor">#FFFFFF</Color>
    <!--  次要区域背景：用于分组、侧边栏等  -->
    <Color x:Key="SecondaryRegionColor">#F3F2F1</Color>
    <!--  三级区域背景：用于更深层次的分组  -->
    <Color x:Key="ThirdlyRegionColor">#FAF9F8</Color>

    <!--  边框颜色 (Border Colors)  -->
    <!--  主边框：用于输入框、按钮等  -->
    <Color x:Key="BorderColor">#C8C6C4</Color>
    <!--  次要边框：用于分割线、卡片边框等  -->
    <Color x:Key="SecondaryBorderColor">#EDEBE9</Color>

    <!--  标题颜色 (Title Colors)  -->
    <Color x:Key="TitleColor">#0078D4</Color>
    <Color x:Key="SecondaryTitleColor">#106EBE</Color>

    <!--  默认控件颜色 (Default Control Colors)  -->
    <Color x:Key="DefaultColor">#FFFFFF</Color>
    <Color x:Key="DarkDefaultColor">#F3F2F1</Color>

    <!--  语义颜色 (Semantic Colors)  -->
    <!--  成功色  -->
    <Color x:Key="LightSuccessColor">#DFF6DD</Color>
    <Color x:Key="SuccessColor">#107C10</Color>
    <Color x:Key="DarkSuccessColor">#175D1F</Color>

    <!--  警告色  -->
    <Color x:Key="LightWarningColor">#FFE6CC</Color>
    <Color x:Key="WarningColor">#FF8C00</Color>
    <Color x:Key="DarkWarningColor">#9E6200</Color>

    <!--  错误色  -->
    <Color x:Key="LightDangerColor">#F9CCCD</Color>
    <Color x:Key="DangerColor">#D13438</Color>
    <Color x:Key="DarkDangerColor">#9E2222</Color>

    <!--  信息色  -->
    <Color x:Key="LightInfoColor">#CCE8F2</Color>
    <Color x:Key="InfoColor">#0099BC</Color>
    <Color x:Key="DarkInfoColor">#00677B</Color>

    <!--  强调色 (Accent Colors)  -->
    <Color x:Key="AccentColor">#8764B8</Color>
    <Color x:Key="DarkAccentColor">#622F9E</Color>

    <!--  遮罩和阴影颜色 (Mask and Shadow Colors)  -->
    <Color x:Key="DarkMaskColor">#0F000000</Color>
    <Color x:Key="DarkOpacityColor">#1A000000</Color>

    <!--  特殊效果  -->
    <system:UInt32 x:Key="BlurGradientValue">0x99FFFFFF</system:UInt32>

    <!--  控件状态颜色 (Control State Colors)  -->
    <!--  悬停状态  -->
    <Color x:Key="HoverBackgroundColor">#EDEBE9</Color>
    <Color x:Key="HoverBorderColor">#8A8886</Color>

    <!--  按下状态  -->
    <Color x:Key="PressedBackgroundColor">#C8C6C4</Color>
    <Color x:Key="PressedBorderColor">#797775</Color>

    <!--  禁用状态  -->
    <Color x:Key="DisabledBackgroundColor">#F3F2F1</Color>
    <Color x:Key="DisabledTextColor">#C8C6C4</Color>
    <Color x:Key="DisabledBorderColor">#EDEBE9</Color>

    <!--  选中状态  -->
    <Color x:Key="SelectedBackgroundColor">#E1ECFC</Color>
    <Color x:Key="SelectedBorderColor">#0078D4</Color>
    <Color x:Key="SelectedTextColor">#004E66</Color>

    <!--  焦点状态  -->
    <Color x:Key="FocusBorderColor">#0078D4</Color>
    <Color x:Key="FocusBackgroundColor">#F3F8FE</Color>

</ResourceDictionary>
