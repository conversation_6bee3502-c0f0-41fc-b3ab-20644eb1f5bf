using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Windows.Input;
using AirMonitor.Services;

namespace AirMonitor.ViewModels
{
    /// <summary>
    /// 主窗口视图模型
    /// </summary>
    public class MainWindowViewModel : ViewModelBase
    {
        private readonly ApplicationSettings _appSettings;
        private readonly ThemeService _themeService;

        public MainWindowViewModel(ILogger<MainWindowViewModel> logger, IOptions<ApplicationSettings> appSettings, ThemeService themeService)
            : base(logger)
        {
            _appSettings = appSettings.Value;
            _themeService = themeService;
            Title = $"{_appSettings.Name} v{_appSettings.Version}";

            // 订阅主题变更事件
            _themeService.ThemeChanged += OnThemeChanged;

            // 初始化命令
            InitializeCommands();

            Logger.LogInformation("MainWindowViewModel 已创建");
        }

        #region 属性

        /// <summary>
        /// 应用程序名称
        /// </summary>
        public string ApplicationName => _appSettings.Name;

        /// <summary>
        /// 应用程序版本
        /// </summary>
        public string ApplicationVersion => _appSettings.Version;

        /// <summary>
        /// 应用程序描述
        /// </summary>
        public string ApplicationDescription => _appSettings.Description;

        /// <summary>
        /// 状态栏消息
        /// </summary>
        private string _statusMessage = "就绪";
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// 当前时间
        /// </summary>
        private DateTime _currentTime = DateTime.Now;
        public DateTime CurrentTime
        {
            get => _currentTime;
            set => SetProperty(ref _currentTime, value);
        }

        #endregion

        #region 命令

        /// <summary>
        /// 退出应用程序命令
        /// </summary>
        public ICommand ExitCommand { get; private set; } = null!;

        /// <summary>
        /// 关于命令
        /// </summary>
        public ICommand AboutCommand { get; private set; } = null!;

        /// <summary>
        /// 切换到浅色主题命令
        /// </summary>
        public ICommand SwitchToLightThemeCommand { get; private set; } = null!;

        /// <summary>
        /// 切换到深色主题命令
        /// </summary>
        public ICommand SwitchToDarkThemeCommand { get; private set; } = null!;

        /// <summary>
        /// 切换主题命令
        /// </summary>
        public ICommand ToggleThemeCommand { get; private set; } = null!;

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            ExitCommand = new RelayCommand(ExecuteExit);
            AboutCommand = new RelayCommand(ExecuteAbout);
            SwitchToLightThemeCommand = new RelayCommand(ExecuteSwitchToLightTheme);
            SwitchToDarkThemeCommand = new RelayCommand(ExecuteSwitchToDarkTheme);
            ToggleThemeCommand = new RelayCommand(ExecuteToggleTheme);
        }

        /// <summary>
        /// 执行退出命令
        /// </summary>
        private void ExecuteExit()
        {
            Logger.LogInformation("用户请求退出应用程序");
            System.Windows.Application.Current.Shutdown();
        }

        /// <summary>
        /// 执行关于命令
        /// </summary>
        private void ExecuteAbout()
        {
            Logger.LogInformation("显示关于信息");
            StatusMessage = $"关于 {ApplicationName} - {ApplicationDescription}";
        }

        /// <summary>
        /// 执行切换到浅色主题命令
        /// </summary>
        private void ExecuteSwitchToLightTheme()
        {
            Logger.LogInformation("切换到浅色主题");
            _themeService.SwitchTheme(ThemeService.ThemeType.Light);
        }

        /// <summary>
        /// 执行切换到深色主题命令
        /// </summary>
        private void ExecuteSwitchToDarkTheme()
        {
            Logger.LogInformation("切换到深色主题");
            _themeService.SwitchTheme(ThemeService.ThemeType.Dark);
        }

        /// <summary>
        /// 执行切换主题命令
        /// </summary>
        private void ExecuteToggleTheme()
        {
            Logger.LogInformation("切换主题");
            _themeService.ToggleTheme();
        }

        /// <summary>
        /// 主题变更事件处理
        /// </summary>
        private void OnThemeChanged(object? sender, ThemeService.ThemeType theme)
        {
            var themeName = _themeService.GetThemeDisplayName(theme);
            StatusMessage = $"已切换到{themeName}";
            Logger.LogInformation($"主题已切换到: {themeName}");
        }

        #endregion

        #region 重写方法

        public override void Initialize()
        {
            base.Initialize();
            StatusMessage = "应用程序已启动";
            CurrentTime = DateTime.Now;
            
            // 启动时间更新定时器（这里只是示例，实际项目中可能需要更复杂的时间管理）
            var timer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            timer.Tick += (s, e) => CurrentTime = DateTime.Now;
            timer.Start();
        }

        public override void Cleanup()
        {
            // 取消订阅主题变更事件
            _themeService.ThemeChanged -= OnThemeChanged;

            base.Cleanup();
            Logger.LogInformation("MainWindowViewModel 清理完成");
        }

        #endregion
    }
}
