﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <Style x:Key="PathBaseStyle" TargetType="Path">
        <Setter Property="Stretch" Value="Uniform"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="FlowDirection" Value="LeftToRight"/>
    </Style>

    <!--搜索-->
    <Style x:Key="SearchPathStyle" BasedOn="{StaticResource PathBaseStyle}" TargetType="Path">
        <Setter Property="Data" Value="{StaticResource SearchGeometry}"/>
    </Style>

    <!--全屏返回-->
    <Style x:Key="FullScreenReturnPathStyle" BasedOn="{StaticResource PathBaseStyle}" TargetType="Path">
        <Setter Property="Data" Value="{StaticResource FullScreenReturnGeometry}"/>
    </Style>

    <!--全屏-->
    <Style x:Key="FullScreenPathStyle" BasedOn="{StaticResource PathBaseStyle}" TargetType="Path">
        <Setter Property="Data" Value="{StaticResource FullScreenGeometry}"/>
    </Style>

    <!--windows-->
    <Style x:Key="WindowsPathStyle" BasedOn="{StaticResource PathBaseStyle}" TargetType="Path">
        <Setter Property="Data" Value="{StaticResource WindowsGeometry}"/>
    </Style>

    <!--保存-->
    <Style x:Key="SavePathStyle" BasedOn="{StaticResource PathBaseStyle}" TargetType="Path">
        <Setter Property="Data" Value="{StaticResource SaveGeometry}"/>
    </Style>

    <!--下载-->
    <Style x:Key="DownloadPathStyle" BasedOn="{StaticResource PathBaseStyle}" TargetType="Path">
        <Setter Property="Data" Value="{StaticResource DownloadGeometry}"/>
    </Style>

    <!--放大-->
    <Style x:Key="EnlargePathStyle" BasedOn="{StaticResource PathBaseStyle}" TargetType="Path">
        <Setter Property="Data" Value="{StaticResource EnlargeGeometry}"/>
    </Style>

    <!--缩小-->
    <Style x:Key="ReducePathStyle" BasedOn="{StaticResource PathBaseStyle}" TargetType="Path">
        <Setter Property="Data" Value="{StaticResource ReduceGeometry}"/>
    </Style>

    <!--向左旋转-->
    <Style x:Key="RotatePathStyle" BasedOn="{StaticResource PathBaseStyle}" TargetType="Path">
        <Setter Property="Data" Value="{StaticResource RotateLeftGeometry}"/>
    </Style>

    <!--日历-->
    <Style x:Key="CalendarPathStyle" BasedOn="{StaticResource PathBaseStyle}" TargetType="Path">
        <Setter Property="Data" Value="{StaticResource CalendarGeometry}"/>
    </Style>

    <!--删除-->
    <Style x:Key="DeletePathStyle" BasedOn="{StaticResource PathBaseStyle}" TargetType="Path">
        <Setter Property="Data" Value="{StaticResource DeleteGeometry}"/>
    </Style>

    <!--关闭-->
    <Style x:Key="ClosePathStyle" BasedOn="{StaticResource PathBaseStyle}" TargetType="Path">
        <Setter Property="Data" Value="{StaticResource CloseGeometry}"/>
    </Style>

    <!--下-->
    <Style x:Key="DownPathStyle" BasedOn="{StaticResource PathBaseStyle}" TargetType="Path">
        <Setter Property="Data" Value="{StaticResource DownGeometry}"/>
    </Style>

    <!--时钟-->
    <Style x:Key="ClockPathStyle" BasedOn="{StaticResource PathBaseStyle}" TargetType="Path">
        <Setter Property="Data" Value="{StaticResource ClockGeometry}"/>
    </Style>

    <!--向左-->
    <Style x:Key="LeftPathStyle" BasedOn="{StaticResource PathBaseStyle}" TargetType="Path">
        <Setter Property="Data" Value="{StaticResource LeftGeometry}"/>
    </Style>

    <!--向右-->
    <Style x:Key="RightPathStyle" BasedOn="{StaticResource PathBaseStyle}" TargetType="Path">
        <Setter Property="RenderTransformOrigin" Value="0.5, 0.5"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <TransformGroup>
                    <ScaleTransform ScaleY="1" ScaleX="-1"/>
                    <SkewTransform AngleY="0" AngleX="0"/>
                    <RotateTransform Angle="0"/>
                    <TranslateTransform/>
                </TransformGroup>
            </Setter.Value>
        </Setter>
        <Setter Property="Data" Value="{StaticResource LeftGeometry}"/>
    </Style>

    <!--上和下的组合-->
    <Style x:Key="UpDownPathStyle" BasedOn="{StaticResource PathBaseStyle}" TargetType="Path">
        <Setter Property="Data" Value="{StaticResource UpDownGeometry}"/>
    </Style>

</ResourceDictionary>
