﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:o="http://schemas.microsoft.com/winfx/2006/xaml/presentation/options">

    <SolidColorBrush o:Freeze="True" x:Key="LightPrimaryBrush" Color="{DynamicResource LightPrimaryColor}"/>
    <LinearGradientBrush o:Freeze="True" x:Key="PrimaryBrush" EndPoint="1,0" StartPoint="0,0">
        <GradientStop o:Freeze="True" Color="{DynamicResource PrimaryColor}" Offset="0"/>
        <GradientStop o:Freeze="True" Color="{DynamicResource DarkPrimaryColor}" Offset="1"/>
    </LinearGradientBrush>
    <SolidColorBrush o:Freeze="True" x:Key="DarkPrimaryBrush" Color="{DynamicResource DarkPrimaryColor}"/>

    <SolidColorBrush o:Freeze="True" x:Key="PrimaryTextBrush" Color="{DynamicResource PrimaryTextColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="SecondaryTextBrush" Color="{DynamicResource SecondaryTextColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="ThirdlyTextBrush" Color="{DynamicResource ThirdlyTextColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="ReverseTextBrush" Color="{DynamicResource ReverseTextColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="TextIconBrush" Color="{DynamicResource TextIconColor}"/>

    <SolidColorBrush o:Freeze="True" x:Key="BorderBrush" Color="{DynamicResource BorderColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="SecondaryBorderBrush" Color="{DynamicResource SecondaryBorderColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="BackgroundBrush" Color="{DynamicResource BackgroundColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="RegionBrush" Color="{DynamicResource RegionColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="SecondaryRegionBrush" Color="{DynamicResource SecondaryRegionColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="ThirdlyRegionBrush" Color="{DynamicResource ThirdlyRegionColor}"/>
    <LinearGradientBrush o:Freeze="True" x:Key="TitleBrush" EndPoint="1,0" StartPoint="0,0">
        <GradientStop o:Freeze="True" Color="{DynamicResource TitleColor}" Offset="0"/>
        <GradientStop o:Freeze="True" Color="{DynamicResource SecondaryTitleColor}" Offset="1"/>
    </LinearGradientBrush>

    <SolidColorBrush o:Freeze="True" x:Key="DefaultBrush" Color="{DynamicResource DefaultColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="DarkDefaultBrush" Color="{DynamicResource DarkDefaultColor}"/>

    <SolidColorBrush o:Freeze="True" x:Key="LightDangerBrush" Color="{DynamicResource LightDangerColor}"/>
    <LinearGradientBrush o:Freeze="True" x:Key="DangerBrush" EndPoint="1,0" StartPoint="0,0">
        <GradientStop o:Freeze="True" Color="{DynamicResource DangerColor}" Offset="0"/>
        <GradientStop o:Freeze="True" Color="{DynamicResource DarkDangerColor}" Offset="1"/>
    </LinearGradientBrush>
    <SolidColorBrush o:Freeze="True" x:Key="DarkDangerBrush" Color="{DynamicResource DarkDangerColor}"/>

    <SolidColorBrush o:Freeze="True" x:Key="LightWarningBrush" Color="{DynamicResource LightWarningColor}"/>
    <LinearGradientBrush o:Freeze="True" x:Key="WarningBrush" EndPoint="1,0" StartPoint="0,0">
        <GradientStop o:Freeze="True" Color="{DynamicResource WarningColor}" Offset="0"/>
        <GradientStop o:Freeze="True" Color="{DynamicResource DarkWarningColor}" Offset="1"/>
    </LinearGradientBrush>
    <SolidColorBrush o:Freeze="True" x:Key="DarkWarningBrush" Color="{DynamicResource DarkWarningColor}"/>

    <SolidColorBrush o:Freeze="True" x:Key="LightInfoBrush" Color="{DynamicResource LightInfoColor}"/>
    <LinearGradientBrush o:Freeze="True" x:Key="InfoBrush" EndPoint="1,0" StartPoint="0,0">
        <GradientStop o:Freeze="True" Color="{DynamicResource InfoColor}" Offset="0"/>
        <GradientStop o:Freeze="True" Color="{DynamicResource DarkInfoColor}" Offset="1"/>
    </LinearGradientBrush>
    <SolidColorBrush o:Freeze="True" x:Key="DarkInfoBrush" Color="{DynamicResource DarkInfoColor}"/>

    <SolidColorBrush o:Freeze="True" x:Key="LightSuccessBrush" Color="{DynamicResource LightSuccessColor}"/>
    <LinearGradientBrush o:Freeze="True" x:Key="SuccessBrush" EndPoint="1,0" StartPoint="0,0">
        <GradientStop o:Freeze="True" Color="{DynamicResource SuccessColor}" Offset="0"/>
        <GradientStop o:Freeze="True" Color="{DynamicResource DarkSuccessColor}" Offset="1"/>
    </LinearGradientBrush>
    <SolidColorBrush o:Freeze="True" x:Key="DarkSuccessBrush" Color="{DynamicResource DarkSuccessColor}"/>

    <SolidColorBrush o:Freeze="True" x:Key="AccentBrush" Color="{DynamicResource AccentColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="DarkAccentBrush" Color="{DynamicResource DarkAccentColor}"/>

    <SolidColorBrush o:Freeze="True" x:Key="DarkMaskBrush" Color="{DynamicResource DarkMaskColor}"/>
    <SolidColorBrush o:Freeze="True" x:Key="DarkOpacityBrush" Color="{DynamicResource DarkOpacityColor}"/>

</ResourceDictionary>