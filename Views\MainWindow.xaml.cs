using AirMonitor.ViewModels;
using Microsoft.Extensions.Logging;
using System.Windows;

namespace AirMonitor.Views
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow
    {
        private readonly MainWindowViewModel _viewModel;
        private readonly ILogger<MainWindow> _logger;

        public MainWindow(MainWindowViewModel viewModel, ILogger<MainWindow> logger)
        {
            InitializeComponent();
            
            _viewModel = viewModel;
            _logger = logger;
            
            // 设置数据上下文
            DataContext = _viewModel;
            
            _logger.LogInformation("MainWindow 已创建");
        }

        protected override void OnSourceInitialized(System.EventArgs e)
        {
            base.OnSourceInitialized(e);
            
            // 初始化 ViewModel
            _viewModel.Initialize();
            
            _logger.LogInformation("MainWindow 已初始化");
        }

        protected override void OnClosed(System.EventArgs e)
        {
            // 清理 ViewModel
            _viewModel.Cleanup();
            
            _logger.LogInformation("MainWindow 已关闭");
            
            base.OnClosed(e);
        }
    }
}
