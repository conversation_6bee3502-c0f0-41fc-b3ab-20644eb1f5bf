<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <Nullable>enable</Nullable>
    <AssemblyTitle>AirMonitor</AssemblyTitle>
    <AssemblyDescription>商用监控调试桌面应用程序</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Company>AirMonitor</Company>
    <Product>AirMonitor</Product>
    <Copyright>Copyright © 2024</Copyright>
  </PropertyGroup>

  <ItemGroup>
    <!-- 依赖注入 -->
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />
    
    <!-- MVVM框架 -->
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    
    <!-- 日志系统 -->
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    
    <!-- 配置管理 -->
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.6" />
    
    <!-- UI控件库 -->
    <PackageReference Include="HandyControl" Version="3.5.1" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
