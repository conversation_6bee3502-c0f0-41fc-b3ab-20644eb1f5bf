<hc:Window x:Class="AirMonitor.Views.MainWindow"
           xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
           xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
           xmlns:hc="https://handyorg.github.io/handycontrol"
           xmlns:vm="clr-namespace:AirMonitor.ViewModels"
           Title="{Binding Title}"
           Height="600" 
           Width="900"
           MinHeight="400"
           MinWidth="600"
           WindowStartupLocation="CenterScreen"
           ShowNonClientArea="True"
           NonClientAreaBackground="{DynamicResource PrimaryBrush}"
           NonClientAreaForeground="White">

    <!-- DataContext 将通过依赖注入在代码后置中设置 -->

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 菜单栏 -->
        <Menu Grid.Row="0">
            <MenuItem Header="文件(_F)">
                <MenuItem Header="退出(_X)" Command="{Binding ExitCommand}"/>
            </MenuItem>
            <MenuItem Header="主题(_T)">
                <MenuItem Header="浅色主题(_L)" Command="{Binding SwitchToLightThemeCommand}"/>
                <MenuItem Header="深色主题(_D)" Command="{Binding SwitchToDarkThemeCommand}"/>
                <Separator/>
                <MenuItem Header="切换主题(_S)" Command="{Binding ToggleThemeCommand}"/>
            </MenuItem>
            <MenuItem Header="帮助(_H)">
                <MenuItem Header="关于(_A)" Command="{Binding AboutCommand}"/>
            </MenuItem>
        </Menu>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 应用程序信息 -->
            <hc:Card Grid.Row="0" Margin="0,0,0,20" Padding="20">
                <StackPanel>
                    <TextBlock Text="{Binding ApplicationName}"
                               FontSize="24"
                               FontWeight="Bold"
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding ApplicationDescription}"
                               FontSize="14"
                               HorizontalAlignment="Center"
                               Margin="0,5,0,0"/>
                    <TextBlock Text="{Binding ApplicationVersion, StringFormat='版本: {0}'}"
                               FontSize="12"
                               HorizontalAlignment="Center"
                               Margin="0,5,0,0"/>
                </StackPanel>
            </hc:Card>

            <!-- 主工作区域 - Fluent Design配色系统测试 -->
            <hc:ScrollViewer Grid.Row="1">
                <StackPanel Margin="10">

                    <!-- 主题信息卡片 -->
                    <hc:Card Margin="0,0,0,15" Padding="20">
                        <StackPanel>
                            <TextBlock Text="Fluent Design 配色系统测试"
                                       FontSize="20"
                                       FontWeight="Bold"
                                       Foreground="{DynamicResource PrimaryTextBrush}"
                                       HorizontalAlignment="Center"/>
                            <TextBlock Text="{Binding CurrentTime, StringFormat='当前时间: yyyy-MM-dd HH:mm:ss'}"
                                       FontSize="12"
                                       Foreground="{DynamicResource SecondaryTextBrush}"
                                       HorizontalAlignment="Center"
                                       Margin="0,10,0,0"/>
                        </StackPanel>
                    </hc:Card>

                    <!-- 按钮测试区域 -->
                    <hc:Card Margin="0,0,0,15" Padding="20">
                        <StackPanel>
                            <TextBlock Text="按钮控件测试" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                            <WrapPanel>
                                <Button Content="默认按钮" Margin="5" Padding="10,5"/>
                                <Button Content="主要按钮" Background="{DynamicResource PrimaryBrush}" Foreground="White" Margin="5" Padding="10,5"/>
                                <Button Content="成功按钮" Background="{DynamicResource SuccessBrush}" Foreground="White" Margin="5" Padding="10,5"/>
                                <Button Content="警告按钮" Background="{DynamicResource WarningBrush}" Foreground="White" Margin="5" Padding="10,5"/>
                                <Button Content="危险按钮" Background="{DynamicResource DangerBrush}" Foreground="White" Margin="5" Padding="10,5"/>
                                <Button Content="信息按钮" Background="{DynamicResource InfoBrush}" Foreground="White" Margin="5" Padding="10,5"/>
                                <Button Content="禁用按钮" IsEnabled="False" Margin="5" Padding="10,5"/>
                            </WrapPanel>
                        </StackPanel>
                    </hc:Card>

                    <!-- 输入控件测试区域 -->
                    <hc:Card Margin="0,0,0,15" Padding="20">
                        <StackPanel>
                            <TextBlock Text="输入控件测试" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                    <TextBox hc:InfoElement.Placeholder="请输入文本" Margin="0,5"/>
                                    <PasswordBox hc:InfoElement.Placeholder="请输入密码" Margin="0,5"/>
                                    <hc:NumericUpDown Value="100" Margin="0,5"/>
                                    <DatePicker Margin="0,5"/>
                                </StackPanel>
                                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                    <ComboBox hc:InfoElement.Placeholder="请选择选项" Margin="0,5">
                                        <ComboBoxItem Content="选项1"/>
                                        <ComboBoxItem Content="选项2"/>
                                        <ComboBoxItem Content="选项3"/>
                                    </ComboBox>
                                    <hc:SearchBar hc:InfoElement.Placeholder="搜索..." Margin="0,5"/>
                                    <CheckBox Content="复选框选项" Margin="0,5"/>
                                    <RadioButton Content="单选按钮" Margin="0,5"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </hc:Card>

                </StackPanel>
            </hc:ScrollViewer>
        </Grid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Text="{Binding CurrentTime, StringFormat=HH:mm:ss}"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</hc:Window>
